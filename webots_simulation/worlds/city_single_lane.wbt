#VRML_SIM R2023a utf8


EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Oak.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Pine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Cypress.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Sassafras.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/BigSassafras.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/obstacles/protos/OilBarrel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Crossroad.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Windmill.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadIntersection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Church.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "../protos/VelodyneVLP-16.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Plastic.proto"
WorldInfo {
  info [
    "Autonomous Vehicle Simulation"
"The simple controller example uses an on-board camera to follow the yellow road lines and a SICK sensor to avoid the obstacles."
"The control of the vehicle is done using the driver library."
"The vehicle based on the Car PROTO is modelled with realistic physics properties: motor torques, body mass, friction forces, suspensions, etc."
  ]
  title "City"
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation 0.085120428060967 0.9963704829056774 0.0006111636645422707 1.1495243681571639
  position -104.84995552898478 22.931879317525766 239.88724751948394
  near 1
  followType "None"
  followSmoothness 0
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.38 0.35 0.32
  visibilityRange 1000
}
DEF GROUND Solid {
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  boundingObject USE GROUND_PLANE
  locked TRUE
}
RoadIntersection {
  translation 70 -40 0.02
  rotation 0 0 1 0.785
  roadsWidth 12
  startRoadsLength [
    4 
  ]
}
Windmill {
  translation 126.05 0 0
}
StraightRoadSegment {
  translation -50 30 0.02
  rotation 0 0 1 -1.5723853071795864
  name "road(1)"
  id "1"
  startJunction "25"
  endJunction "24"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 60
}
StraightRoadSegment {
  translation -40 -40 0.02
  name "road(6)"
  id "3"
  startJunction "23"
  endJunction "22"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 100
}
StraightRoadSegment {
  translation 70 30 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "28"
  endJunction "16"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 60
}
StraightRoadSegment {
  translation -40 40 0.02
  name "road(7)"
  id "7"
  startJunction "17"
  endJunction "29"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 100
}
CurvedRoadSegment {
  translation -40 30 0.02
  rotation 0 0 1 1.5708
  name "road(15)"
  id "15"
  startJunction "17"
  endJunction "18"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  roadBorderWidth [
    0.6 
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
}
CurvedRoadSegment {
  translation -40 -30 0.02
  rotation 0 0 1 3.1415
  name "road(2)"
  id "15"
  startJunction "17"
  endJunction "18"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  roadBorderWidth [
    0.6 
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
}
CurvedRoadSegment {
  translation 80 -30 0.02
  rotation 0 0 1 -1.5707
  name "road(4)"
  id "15"
  startJunction "17"
  endJunction "18"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  roadBorderWidth [
    0.6 
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
}
CurvedRoadSegment {
  translation 60 30 0.02
  name "road(3)"
  id "15"
  startJunction "17"
  endJunction "18"
  width 12
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  roadBorderWidth [
    0.6 
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
}
CommercialBuilding {
  translation 129.887 31.6315 0
}
UBuilding {
  translation -87.1466 33.3827 0
}
UBuilding {
  translation -68.3283 -69.0739 0
  rotation 0 0 1 -1.8325953071795862
  name "U building(1)"
}
HollowBuilding {
}
HollowBuilding {
  translation 148.81 0 0
  name "hollow building(1)"
}
Hotel {
  translation -9.97953 71.6228 0
}
TheThreeTowers {
  translation 99.408 -1.186 0
}
TheThreeTowers {
  translation -60.806 89.0985 0
  rotation 0 0 1 2.09439
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation -68.472 -21.0861 0
  rotation 0 0 1 1.5708
  name "big glass tower(1)"
}
Museum {
  translation -0.191204 -63.5771 0
  rotation 0 0 1 1.5708
}
Museum {
  translation -23.6081 -108.826 0
  rotation 0 0 -1 -1.3090053071795866
  name "museum(1)"
}
ResidentialBuilding {
  translation 63.5929 -90.9791 0
  rotation 0 0 -1 -1.8325953071795862
}
ResidentialBuilding {
  translation 87.476 64.8167 0
  name "residential building(1)"
}
Oak {
  translation 81.7751 -19.8126 0
}
Pine {
  translation 50.4097 -51.4907 0
  name "tree(1)"
}
Cypress {
  translation 56.567 -51.5363 0
  name "tree(2)"
}
Sassafras {
  translation -36.8744 -75.9885 0
  name "tree(3)"
}
BigSassafras {
  translation -35.4355 -54.5924 0
  name "tree(4)"
}
Oak {
  translation 61.566 5.24018 0
  name "tree(5)"
}
Pine {
  translation -26.6541 -68.7408 0
  name "tree(6)"
}
Cypress {
  translation 26.6454 -62.6042 0
  name "tree(7)"
}
Sassafras {
  translation 68.1255 54.8178 0
  name "tree(8)"
}
BigSassafras {
  translation 41.996 28.9863 0
  name "tree(9)"
}
Pine {
  translation 4.80322 63.11 0
  name "tree(11)"
}
Sassafras {
  translation -40.8655 -14.7388 0
  name "tree(13)"
}
BigSassafras {
  translation -26.5327 -26.3803 0
  name "tree(14)"
}
Oak {
  translation -41.0528 22.9321 0
  name "tree(15)"
}
Pine {
  translation 55.7309 28.477 0
  name "tree(16)"
}
Cypress {
  translation -37.764 28.9905 0
  name "tree(17)"
}
Sassafras {
  translation -32.2671 25.676 0
  name "tree(18)"
}
BigSassafras {
  translation 121.921 33.1395 0
  name "tree(19)"
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(1)"
}
CautionSign {
  translation 84.01191 -26.81263 0
  rotation 0 0 1 0.6545
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
  signBoards [
    StopPanel {
      translation 0 0 -0.097
    }
    OrderPanel {
      translation -0.03 0 -0.11
      rotation 0 0 1 3.1415926
    }
  ]
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.317445 79.098744 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
TrafficCone {
  translation 78.00000000746007 -43.00000000004365 -0.004076718672611196
  rotation -0.00027273994835409207 0.9999998767610707 0.00041483823827747794 0.013690792383240252
  name "traffic cone(8)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 78.00000000434133 -45.000000000041474 -0.004076718672611196
  rotation -0.00027308139282249436 0.999999854731967 0.00046471776138642175 0.013690792683564641
  name "traffic cone(9)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 78.00000000518159 -40.00000000004199 -0.004076718672611196
  rotation -0.00027313481933017355 0.9999998510599081 0.0004725225202864723 0.013690792733639172
  name "traffic cone(10)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 78.00000000356867 -35.410900000041224 -0.004076718672611196
  rotation -0.00022778017003426837 0.999981049079731 -0.006152202662077672 0.013691050312063387
  name "traffic cone(11)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 78.00000000361696 -38.00000000004131 -0.004076718672611196
  rotation -0.00018161075422764628 0.999916850369131 -0.012894160128158898 0.013691929476038507
  name "traffic cone(18)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 58.1088 -27.689 0
  rotation 0 0 1 -2.6179953071795863
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
OilBarrel {
  translation -48.63923767475055 20.190364382021976 0.5998773750000002
  rotation 6.110203551471353e-19 4.663312969893398e-19 1 3.0106696069416095
  name "oil barrel(7)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
Crossroad {
  translation -4.5001488 105 0
  id "18"
  shape []
  connectedRoadIDs [
    "15"
"14"
  ]
}
Crossroad {
  translation 45 4.5000744 0
  name "crossroad(10)"
  id "28"
  shape []
  connectedRoadIDs [
    "6"
"5"
  ]
}
Crossroad {
  translation 4.4998512 45.00011 0
  name "crossroad(11)"
  id "29"
  shape []
  connectedRoadIDs [
    "7"
"6"
  ]
}
ResidentialTower {
  translation 0 24.86 0
}
LargeResidentialTower {
  translation 46.21 -2.79 0
  rotation 0 1 0 0
  name "residential tower(1)"
}
ComposedHouse {
  translation -32.4929 5.18087 0
  rotation 0 0 1 -3.1415853071795863
}
Church {
  translation 22.6 -24.34 0
}
Robot {
  name "supervisor"
  controller "supervisor_control"
  supervisor TRUE
}
DEF WEBOTS_VEHICLE0 chitu {
  translation -29.99359858395997 36.62344247452425 0.2349555292158525
  rotation 0.0128964068084894 -0.9317338367880852 -0.36291285465740064 0.010894182572324291
  extensionSlot [
    Group {
      children [
        Accelerometer {
        }
        Gyro {
        }
        InertialUnit {
        }
      ]
    }
    VelodyneVLP-16 {
      translation 2.3 0 1.65
      rotation 0 0 1 -1.5707
      name "laser_top"
    }
    Lidar {
      numberOfLayers 16
      maxRange 100
      type "rotating"
    }
    Camera {
      translation 2.5 0 1.5
      width 640
      height 480
    }
    Transform {
      translation 1.05 0 -0.25
      children [
        Shape {
          appearance Plastic {
          }
          geometry Mesh {
            url [
              "../protos/chitu_coma_rotate.dae"
            ]
          }
          castShadows FALSE
        }
      ]
    }
  ]
}
DEF CAR1 BmwX5Simple {
  translation 20.05 35.49 0.4
  name "vehicle(1)"
}
DEF CAR2 BmwX5Simple {
  translation -3.81 38.87 0.4
  name "vehicle(2)"
}
DEF CAR3 BmwX5Simple {
  translation 38.95 35.61 1.4
  name "vehicle(3)"
}
