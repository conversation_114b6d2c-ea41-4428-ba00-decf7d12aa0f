<?xml version='1.0' encoding='UTF-8'?>
<edges>
  <edge id="13" from="36" to="Custom21" numLanes="2" width="6.0" shape="75.000294,-239.000000 -174.999706,-239.008163 "/>
  <edge id="1" from="40" to="39" numLanes="2" width="5.375" shape="-105.000000,4.500000 -105.000253,-64.500000 "/>
  <edge id="-1" to="40" from="39" numLanes="2" width="5.375" shape="-105.000253,-64.500000 -105.000000,4.500000 "/>
  <edge id="3" from="38" to="37" numLanes="2" width="5.375" shape="-64.500000,-105.000000 4.500000,-105.000000 "/>
  <edge id="-3" to="38" from="37" numLanes="2" width="5.375" shape="4.500000,-105.000000 -64.500000,-105.000000 "/>
  <edge id="5" from="43" to="25" numLanes="2" width="5.375" shape="45.000000,4.500000 44.999890,-25.500000 "/>
  <edge id="-5" to="43" from="25" numLanes="2" width="5.375" shape="44.999890,-25.500000 45.000000,4.500000 "/>
  <edge id="7" from="26" to="44" numLanes="2" width="5.375" shape="-25.500000,45.000000 4.500000,45.000000 "/>
  <edge id="-7" to="26" from="44" numLanes="2" width="5.375" shape="4.500000,45.000000 -25.500000,45.000000 "/>
  <edge id="9" from="42" to="25" numLanes="2" width="5.375" shape="-4.500000,-45.000000 25.500000,-45.000000 "/>
  <edge id="-9" to="42" from="25" numLanes="2" width="5.375" shape="25.500000,-45.000000 -4.500000,-45.000000 "/>
  <edge id="12" from="27" to="28" numLanes="2" width="5.375" shape="85.500000,93.000000 76.500000,93.000294 "/>
  <edge id="-12" to="27" from="28" numLanes="2" width="5.375" shape="76.500000,93.000294 85.500000,93.000000 "/>
  <edge id="13" from="36" to="Custom22" numLanes="2" width="5.375" shape="75.000000,-187.500000 -175.000000,-187.508163 "/>
  <edge id="-13" to="36" from="Custom22" numLanes="2" width="5.375" shape="-175.000000,-187.508163 75.000000,-187.500000 "/>
  <edge id="14" from="34" to="35" numLanes="2" width="5.375" shape="165.000000,52.500000 164.999449,-97.500000 "/>
  <edge id="-14" to="34" from="35" numLanes="2" width="5.375" shape="164.999449,-97.500000 165.000000,52.500000 "/>
  <edge id="15" from="32" to="Custom23" numLanes="2" width="5.5" shape="64.499910,228.250000 -135.500090,228.256531 "/>
  <edge id="-15" to="32" from="Custom23" numLanes="1" width="5.5" shape="-135.500090,228.256531 64.499910,228.250000 "/>
  <edge id="16" from="27" to="33" numLanes="2" width="5.375" shape="105.000000,112.500000 105.000493,190.500000 "/>
  <edge id="-16" to="27" from="33" numLanes="2" width="5.375" shape="105.000493,190.500000 105.000000,112.500000 "/>
  <edge id="17" from="45" to="27" numLanes="2" width="5.375" shape="105.000000,-4.500000 105.000493,73.500000 "/>
  <edge id="-17" to="45" from="27" numLanes="2" width="5.375" shape="105.000493,73.500000 105.000000,-4.500000 "/>
  <edge id="23" from="26" to="41" numLanes="2" width="5.375" shape="-45.000000,25.500000 -45.000110,-4.500000 "/>
  <edge id="-23" to="26" from="41" numLanes="2" width="5.375" shape="-45.000110,-4.500000 -45.000000,25.500000 "/>
  <edge id="24" from="26" to="31" numLanes="2" width="5.375" shape="-45.000000,64.500000 -45.000253,133.500000 "/>
  <edge id="-24" to="26" from="31" numLanes="2" width="5.375" shape="-45.000253,133.500000 -45.000000,64.500000 "/>
  <edge id="0" from="40" to="26" numLanes="2" width="5.375" shape="-105.000000,4.499851 -104.804995,8.469555 -104.221829,12.401030 -103.256119,16.256414 -101.917164,19.998576 -100.217859,23.591478 -98.174571,27.000517 -95.806976,30.192863 -93.137877,33.137772 -90.192978,35.806882 -87.000641,38.174488 -83.591609,40.217789 -79.998713,41.917107 -76.256556,43.256076 -72.401176,44.221800 -68.469703,44.804981 -64.500000,45.000000 "/>
  <edge id="-0" to="40" from="26" numLanes="2" width="5.375" shape="-64.500000,45.000000 -68.469703,44.804981 -72.401176,44.221800 -76.256556,43.256076 -79.998713,41.917107 -83.591609,40.217789 -87.000641,38.174488 -90.192978,35.806882 -93.137877,33.137772 -95.806976,30.192863 -98.174571,27.000517 -100.217859,23.591478 -101.917164,19.998576 -103.256119,16.256414 -104.221829,12.401030 -104.804995,8.469555 -105.000000,4.499851 "/>
  <edge id="2" from="38" to="39" numLanes="2" width="5.375" shape="-64.501322,-105.000000 -68.471020,-104.804851 -72.402473,-104.221542 -76.257822,-103.255692 -79.999935,-101.916601 -83.592775,-100.217166 -87.001740,-98.173754 -90.194001,-95.806043 -93.138812,-93.136837 -95.807815,-90.191841 -98.175306,-86.999418 -100.218483,-83.590311 -101.917670,-79.997354 -103.256503,-76.255148 -104.222087,-72.399733 -104.805125,-68.468239 -105.000000,-64.498529 "/>
  <edge id="-2" to="38" from="39" numLanes="2" width="5.375" shape="-105.000000,-64.498529 -104.805125,-68.468239 -104.222087,-72.399733 -103.256503,-76.255148 -101.917670,-79.997354 -100.218483,-83.590311 -98.175306,-86.999418 -95.807815,-90.191841 -93.138812,-93.136837 -90.194001,-95.806043 -87.001740,-98.173754 -83.592775,-100.217166 -79.999935,-101.916601 -76.257822,-103.255692 -72.402473,-104.221542 -68.471020,-104.804851 -64.501322,-105.000000 "/>
  <edge id="4" from="25" to="37" numLanes="2" width="5.375" shape="45.000000,-64.500149 44.804966,-68.469851 44.221771,-72.401322 43.256032,-76.256698 41.917050,-79.998851 40.217719,-83.591740 38.174406,-87.000765 35.806788,-90.193093 33.137667,-93.137982 30.192748,-95.807071 27.000393,-98.174654 23.591346,-100.217930 19.998438,-101.917221 16.256271,-103.256162 12.400884,-104.221858 8.469407,-104.805010 4.499702,-105.000000 "/>
  <edge id="-4" to="25" from="37" numLanes="2" width="5.375" shape="4.499702,-105.000000 8.469407,-104.805010 12.400884,-104.221858 16.256271,-103.256162 19.998438,-101.917221 23.591346,-100.217930 27.000393,-98.174654 30.192748,-95.807071 33.137667,-93.137982 35.806788,-90.193093 38.174406,-87.000765 40.217719,-83.591740 41.917050,-79.998851 43.256032,-76.256698 44.221771,-72.401322 44.804966,-68.469851 45.000000,-64.500149 "/>
  <edge id="6" from="44" to="43" numLanes="2" width="5.375" shape="4.500000,45.000000 8.469703,44.804981 12.401176,44.221800 16.256556,43.256076 19.998713,41.917107 23.591609,40.217789 27.000641,38.174488 30.192978,35.806882 33.137877,33.137772 35.806976,30.192863 38.174571,27.000517 40.217859,23.591478 41.917164,19.998576 43.256119,16.256414 44.221829,12.401030 44.804995,8.469555 45.000000,4.499851 "/>
  <edge id="-6" to="44" from="43" numLanes="2" width="5.375" shape="45.000000,4.499851 44.804995,8.469555 44.221829,12.401030 43.256119,16.256414 41.917164,19.998576 40.217859,23.591478 38.174571,27.000517 35.806976,30.192863 33.137877,33.137772 30.192978,35.806882 27.000641,38.174488 23.591609,40.217789 19.998713,41.917107 16.256556,43.256076 12.401176,44.221800 8.469703,44.804981 4.500000,45.000000 "/>
  <edge id="8" from="42" to="41" numLanes="2" width="5.375" shape="-4.501322,-45.000000 -8.471020,-44.804851 -12.402473,-44.221542 -16.257822,-43.255692 -19.999935,-41.916601 -23.592775,-40.217166 -27.001740,-38.173754 -30.194001,-35.806043 -33.138812,-33.136837 -35.807815,-30.191841 -38.175306,-26.999418 -40.218483,-23.590311 -41.917670,-19.997354 -43.256503,-16.255148 -44.222087,-12.399733 -44.805125,-8.468239 -45.000000,-4.498529 "/>
  <edge id="-8" to="42" from="41" numLanes="2" width="5.375" shape="-45.000000,-4.498529 -44.805125,-8.468239 -44.222087,-12.399733 -43.256503,-16.255148 -41.917670,-19.997354 -40.218483,-23.590311 -38.175306,-26.999418 -35.807815,-30.191841 -33.138812,-33.136837 -30.194001,-35.806043 -27.001740,-38.173754 -23.592775,-40.217166 -19.999935,-41.916601 -16.257822,-43.255692 -12.402473,-44.221542 -8.471020,-44.804851 -4.501322,-45.000000 "/>
  <edge id="10" from="45" to="25" numLanes="2" width="5.375" shape="105.000000,-4.500149 104.804966,-8.469851 104.221771,-12.401322 103.256032,-16.256698 101.917050,-19.998851 100.217719,-23.591740 98.174406,-27.000765 95.806788,-30.193093 93.137667,-33.137982 90.192748,-35.807071 87.000393,-38.174654 83.591346,-40.217930 79.998438,-41.917221 76.256271,-43.256162 72.400884,-44.221858 68.469407,-44.805010 64.499702,-45.000000 "/>
  <edge id="-10" to="45" from="25" numLanes="2" width="5.375" shape="64.499702,-45.000000 68.469407,-44.805010 72.400884,-44.221858 76.256271,-43.256162 79.998438,-41.917221 83.591346,-40.217930 87.000393,-38.174654 90.192748,-35.807071 93.137667,-33.137982 95.806788,-30.193093 98.174406,-27.000765 100.217719,-23.591740 101.917050,-19.998851 103.256032,-16.256698 104.221771,-12.401322 104.804966,-8.469851 105.000000,-4.500149 "/>
  <edge id="11" from="35" to="36" numLanes="2" width="5.375" shape="165.000000,-97.500331 164.566591,-106.321892 163.270603,-115.058494 161.124516,-123.625997 158.149000,-131.941891 154.372709,-139.926089 149.832013,-147.501699 144.570639,-154.595763 138.639260,-161.139961 132.094996,-167.071268 125.000874,-172.332564 117.425214,-176.873177 109.440974,-180.649379 101.125048,-183.624804 92.557521,-185.770796 83.820905,-187.066688 74.999339,-187.500000 "/>
  <edge id="-11" to="35" from="36" numLanes="2" width="5.375" shape="74.999339,-187.500000 83.820905,-187.066688 92.557521,-185.770796 101.125048,-183.624804 109.440974,-180.649379 117.425214,-176.873177 125.000874,-172.332564 132.094996,-167.071268 138.639260,-161.139961 144.570639,-154.595763 149.832013,-147.501699 154.372709,-139.926089 158.149000,-131.941891 161.124516,-123.625997 163.270603,-115.058494 164.566591,-106.321892 165.000000,-97.500331 "/>
  <edge id="18" from="28" to="29" numLanes="2" width="5.375" shape="76.501322,93.000000 72.531613,93.194890 68.600121,93.777942 64.744709,94.743541 61.002508,96.082387 57.409557,97.781587 54.000459,99.824777 50.808044,102.192279 47.863058,104.861293 45.193863,107.806114 42.826164,110.998383 40.782764,114.407356 39.083342,118.000202 37.744265,121.742321 36.778429,125.597673 36.195135,129.529129 36.000000,133.498826 "/>
  <edge id="-18" to="28" from="29" numLanes="2" width="5.375" shape="36.000000,133.498826 36.195135,129.529129 36.778429,125.597673 37.744265,121.742321 39.083342,118.000202 40.782764,114.407356 42.826164,110.998383 45.193863,107.806114 47.863058,104.861293 50.808044,102.192279 54.000459,99.824777 57.409557,97.781587 61.002508,96.082387 64.744709,94.743541 68.600121,93.777942 72.531613,93.194890 76.501322,93.000000 "/>
  <edge id="19" from="32" to="33" numLanes="2" width="5.375" shape="64.500000,231.000000 68.469703,230.804981 72.401176,230.221800 76.256556,229.256076 79.998713,227.917107 83.591609,226.217789 87.000641,224.174488 90.192978,221.806882 93.137877,219.137772 95.806976,216.192863 98.174571,213.000517 100.217859,209.591478 101.917164,205.998576 103.256119,202.256414 104.221829,198.401030 104.804995,194.469555 105.000000,190.499851 "/>
  <edge id="-19" to="32" from="33" numLanes="2" width="5.375" shape="105.000000,190.499851 104.804995,194.469555 104.221829,198.401030 103.256119,202.256414 101.917164,205.998576 100.217859,209.591478 98.174571,213.000517 95.806976,216.192863 93.137877,219.137772 90.192978,221.806882 87.000641,224.174488 83.591609,226.217789 79.998713,227.917107 76.256556,229.256076 72.401176,230.221800 68.469703,230.804981 64.500000,231.000000 "/>
  <edge id="20" from="27" to="34" numLanes="2" width="5.375" shape="124.500000,93.000000 128.469703,92.804981 132.401176,92.221800 136.256556,91.256076 139.998713,89.917107 143.591609,88.217789 147.000641,86.174488 150.192978,83.806882 153.137877,81.137772 155.806976,78.192863 158.174571,75.000517 160.217859,71.591478 161.917164,67.998576 163.256119,64.256414 164.221829,60.401030 164.804995,56.469555 165.000000,52.499851 "/>
  <edge id="-20" to="27" from="34" numLanes="2" width="5.375" shape="165.000000,52.499851 164.804995,56.469555 164.221829,60.401030 163.256119,64.256414 161.917164,67.998576 160.217859,71.591478 158.174571,75.000517 155.806976,78.192863 153.137877,81.137772 150.192978,83.806882 147.000641,86.174488 143.591609,88.217789 139.998713,89.917107 136.256556,91.256076 132.401176,92.221800 128.469703,92.804981 124.500000,93.000000 "/>
  <edge id="21" from="30" to="29" numLanes="2" width="5.375" shape="-4.500000,174.000000 -0.530297,173.804981 3.401176,173.221800 7.256556,172.256076 10.998713,170.917107 14.591609,169.217789 18.000641,167.174488 21.192978,164.806882 24.137877,162.137772 26.806976,159.192863 29.174571,156.000517 31.217859,152.591478 32.917164,148.998576 34.256119,145.256414 35.221829,141.401030 35.804995,137.469555 36.000000,133.499851 "/>
  <edge id="-21" to="30" from="29" numLanes="2" width="5.375" shape="36.000000,133.499851 35.804995,137.469555 35.221829,141.401030 34.256119,145.256414 32.917164,148.998576 31.217859,152.591478 29.174571,156.000517 26.806976,159.192863 24.137877,162.137772 21.192978,164.806882 18.000641,167.174488 14.591609,169.217789 10.998713,170.917107 7.256556,172.256076 3.401176,173.221800 -0.530297,173.804981 -4.500000,174.000000 "/>
  <edge id="22" from="31" to="30" numLanes="2" width="5.375" shape="-45.000000,133.499851 -44.804995,137.469555 -44.221829,141.401030 -43.256119,145.256414 -41.917164,148.998576 -40.217859,152.591478 -38.174571,156.000517 -35.806976,159.192863 -33.137877,162.137772 -30.192978,164.806882 -27.000641,167.174488 -23.591609,169.217789 -19.998713,170.917107 -16.256556,172.256076 -12.401176,173.221800 -8.469703,173.804981 -4.500000,174.000000 "/>
  <edge id="-22" to="31" from="30" numLanes="2" width="5.375" shape="-4.500000,174.000000 -8.469703,173.804981 -12.401176,173.221800 -16.256556,172.256076 -19.998713,170.917107 -23.591609,169.217789 -27.000641,167.174488 -30.192978,164.806882 -33.137877,162.137772 -35.806976,159.192863 -38.174571,156.000517 -40.217859,152.591478 -41.917164,148.998576 -43.256119,145.256414 -44.221829,141.401030 -44.804995,137.469555 -45.000000,133.499851 "/>
</edges>
