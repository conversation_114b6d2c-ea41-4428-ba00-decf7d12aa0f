<?xml version="1.0" encoding="UTF-8"?>

<!-- generated on Tue Mar 11 11:45:37 2025 by Eclipse SUMO netedit Version 1.4.0
<configuration xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/netconvertConfiguration.xsd">

    <input>
        <sumo-net-file value="/home/<USER>/uautopilot/uautopilot_simulation/webots_simulation/worlds/05biandao/biandao1_net/sumo.net.xml"/>
    </input>

    <output>
        <output-file value="/home/<USER>/uautopilot/uautopilot_simulation/webots_simulation/worlds/05biandao/biandao1_net/sumo.net.xml"/>
    </output>

    <processing>
        <geometry.min-radius.fix.railways value="false"/>
        <geometry.max-grade.fix value="false"/>
        <offset.disable-normalization value="true"/>
        <lefthand value="false"/>
    </processing>

    <junctions>
        <no-turnarounds value="true"/>
        <junctions.corner-detail value="5"/>
        <junctions.limit-turn-speed value="5.5"/>
        <rectangular-lane-cut value="false"/>
    </junctions>

    <pedestrian>
        <walkingareas value="false"/>
    </pedestrian>

    <netedit>
        <route-files value="/home/<USER>/uautopilot/uautopilot_simulation/webots_simulation/worlds/05biandao/biandao1_net/sumo.rou.xml"/>
    </netedit>

    <visualisation>
        <registry-viewport value="true"/>
    </visualisation>

    <report>
        <aggregate-warnings value="5"/>
    </report>

</configuration>
-->

<net version="1.3" junctionCornerDetail="5" limitTurnSpeed="5.50" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:noNamespaceSchemaLocation="http://sumo.dlr.de/xsd/net_file.xsd">

    <location netOffset="175.00,187.51" convBoundary="0.00,-56.93,358.17,422.84" origBoundary="-175.00,-239.01,165.00,231.01" projParameter="!"/>

    <edge id=":25_0" function="internal">
        <lane id=":25_0_0" index="0" speed="5.06" length="4.32" width="5.38" shape="211.93,153.26 211.76,152.09 211.26,151.25 210.42,150.75 209.25,150.58"/>
    </edge>
    <edge id=":25_1" function="internal">
        <lane id=":25_1_0" index="0" speed="13.89" length="21.50" width="5.38" shape="211.93,153.26 212.36,131.76"/>
        <lane id=":25_1_1" index="1" speed="13.89" length="21.50" width="5.38" shape="217.31,153.26 217.75,131.76"/>
    </edge>
    <edge id=":25_3" function="internal">
        <lane id=":25_3_0" index="0" speed="9.60" length="20.93" width="5.38" shape="217.31,153.26 218.51,148.34 221.62,143.80 225.94,140.53 230.75,139.40"/>
    </edge>
    <edge id=":25_4" function="internal">
        <lane id=":25_4_0" index="0" speed="4.73" length="7.86" width="5.38" shape="217.31,153.26 218.66,151.24 220.00,150.57 221.34,151.24 222.69,153.26"/>
    </edge>
    <edge id=":25_5" function="internal">
        <lane id=":25_5_0" index="0" speed="5.14" length="4.70" width="5.38" shape="230.75,150.17 229.58,150.32 228.74,150.88 228.24,151.86 228.07,153.26"/>
    </edge>
    <edge id=":25_6" function="internal">
        <lane id=":25_6_0" index="0" speed="13.89" length="21.50" width="5.38" shape="230.75,150.17 209.25,150.58"/>
        <lane id=":25_6_1" index="1" speed="13.89" length="21.50" width="5.38" shape="230.75,144.78 209.25,145.20"/>
    </edge>
    <edge id=":25_8" function="internal">
        <lane id=":25_8_0" index="0" speed="10.04" length="8.37" width="5.38" shape="230.75,144.78 225.30,143.75 222.91,142.24"/>
    </edge>
    <edge id=":25_9" function="internal">
        <lane id=":25_9_0" index="0" speed="4.74" length="3.93" width="5.38" shape="230.75,144.78 228.73,143.44 228.06,142.09"/>
    </edge>
    <edge id=":25_20" function="internal">
        <lane id=":25_20_0" index="0" speed="10.04" length="12.19" width="5.38" shape="222.91,142.24 221.31,141.23 218.79,137.24 217.75,131.76"/>
    </edge>
    <edge id=":25_21" function="internal">
        <lane id=":25_21_0" index="0" speed="4.74" length="3.93" width="5.38" shape="228.06,142.09 228.73,140.74 230.75,139.40"/>
    </edge>
    <edge id=":25_10" function="internal">
        <lane id=":25_10_0" index="0" speed="4.85" length="3.54" width="5.38" shape="228.52,131.76 228.70,132.71 229.13,133.40 229.82,133.83 230.75,134.01"/>
    </edge>
    <edge id=":25_11" function="internal">
        <lane id=":25_11_0" index="0" speed="13.89" length="21.50" width="5.38" shape="228.52,131.76 228.07,153.26"/>
        <lane id=":25_11_1" index="1" speed="13.89" length="21.50" width="5.38" shape="223.13,131.76 222.69,153.26"/>
    </edge>
    <edge id=":25_13" function="internal">
        <lane id=":25_13_0" index="0" speed="9.60" length="20.95" width="5.38" shape="223.13,131.76 222.01,136.57 218.73,140.89 214.18,144.00 209.25,145.20"/>
    </edge>
    <edge id=":25_14" function="internal">
        <lane id=":25_14_0" index="0" speed="4.74" length="7.87" width="5.38" shape="223.13,131.76 221.79,133.78 220.44,134.45 219.09,133.78 217.75,131.76"/>
    </edge>
    <edge id=":25_15" function="internal">
        <lane id=":25_15_0" index="0" speed="5.15" length="4.72" width="5.38" shape="209.25,134.44 210.66,134.27 211.65,133.77 212.22,132.93 212.36,131.76"/>
    </edge>
    <edge id=":25_16" function="internal">
        <lane id=":25_16_0" index="0" speed="13.89" length="21.50" width="5.38" shape="209.25,134.44 230.75,134.01"/>
        <lane id=":25_16_1" index="1" speed="13.89" length="21.50" width="5.38" shape="209.25,139.82 230.75,139.40"/>
    </edge>
    <edge id=":25_18" function="internal">
        <lane id=":25_18_0" index="0" speed="9.92" length="9.21" width="5.38" shape="209.25,139.82 215.13,140.66 217.93,142.34"/>
    </edge>
    <edge id=":25_19" function="internal">
        <lane id=":25_19_0" index="0" speed="4.73" length="3.93" width="5.38" shape="209.25,139.82 211.27,141.16 211.94,142.51"/>
    </edge>
    <edge id=":25_22" function="internal">
        <lane id=":25_22_0" index="0" speed="9.92" length="12.47" width="5.38" shape="217.93,142.34 219.33,143.18 221.85,147.38 222.69,153.26"/>
    </edge>
    <edge id=":25_23" function="internal">
        <lane id=":25_23_0" index="0" speed="4.73" length="3.93" width="5.38" shape="211.94,142.51 211.27,143.85 209.25,145.20"/>
    </edge>
    <edge id=":26_0" function="internal">
        <lane id=":26_0_0" index="0" speed="4.95" length="3.92" width="5.38" shape="121.93,243.26 121.76,242.34 121.26,241.66 120.42,241.22 119.25,241.03"/>
    </edge>
    <edge id=":26_1" function="internal">
        <lane id=":26_1_0" index="0" speed="13.89" length="21.50" width="5.38" shape="121.93,243.26 121.93,221.76"/>
        <lane id=":26_1_1" index="1" speed="13.89" length="21.50" width="5.38" shape="127.31,243.26 127.31,221.76"/>
    </edge>
    <edge id=":26_3" function="internal">
        <lane id=":26_3_0" index="0" speed="9.68" length="9.54" width="5.38" shape="127.31,243.26 128.52,238.49 131.23,234.75"/>
    </edge>
    <edge id=":26_4" function="internal">
        <lane id=":26_4_0" index="0" speed="4.73" length="3.93" width="5.38" shape="127.31,243.26 128.66,241.24 130.00,240.57"/>
    </edge>
    <edge id=":26_20" function="internal">
        <lane id=":26_20_0" index="0" speed="9.68" length="10.99" width="5.38" shape="131.23,234.75 131.65,234.16 135.98,231.03 140.75,229.82"/>
    </edge>
    <edge id=":26_21" function="internal">
        <lane id=":26_21_0" index="0" speed="4.73" length="3.93" width="5.38" shape="130.00,240.57 131.34,241.24 132.69,243.26"/>
    </edge>
    <edge id=":26_5" function="internal">
        <lane id=":26_5_0" index="0" speed="5.06" length="4.32" width="5.38" shape="140.75,240.58 139.58,240.75 138.74,241.25 138.24,242.09 138.07,243.26"/>
    </edge>
    <edge id=":26_6" function="internal">
        <lane id=":26_6_0" index="0" speed="13.89" length="21.50" width="5.38" shape="140.75,240.58 119.25,241.03"/>
        <lane id=":26_6_1" index="1" speed="13.89" length="21.50" width="5.38" shape="140.75,235.20 119.25,235.64"/>
    </edge>
    <edge id=":26_8" function="internal">
        <lane id=":26_8_0" index="0" speed="9.68" length="20.53" width="5.38" shape="140.75,235.20 135.98,233.99 131.65,230.86 128.52,226.53 127.31,221.76"/>
    </edge>
    <edge id=":26_9" function="internal">
        <lane id=":26_9_0" index="0" speed="4.73" length="7.86" width="5.38" shape="140.75,235.20 138.73,233.85 138.06,232.51 138.73,231.17 140.75,229.82"/>
    </edge>
    <edge id=":26_10" function="internal">
        <lane id=":26_10_0" index="0" speed="5.06" length="4.32" width="5.38" shape="138.07,221.76 138.24,222.93 138.74,223.77 139.58,224.27 140.75,224.44"/>
    </edge>
    <edge id=":26_11" function="internal">
        <lane id=":26_11_0" index="0" speed="13.89" length="21.50" width="5.38" shape="138.07,221.76 138.07,243.26"/>
        <lane id=":26_11_1" index="1" speed="13.89" length="21.50" width="5.38" shape="132.69,221.76 132.69,243.26"/>
    </edge>
    <edge id=":26_13" function="internal">
        <lane id=":26_13_0" index="0" speed="9.60" length="10.12" width="5.38" shape="132.69,221.76 131.49,226.69 128.64,230.86"/>
    </edge>
    <edge id=":26_14" function="internal">
        <lane id=":26_14_0" index="0" speed="4.73" length="3.93" width="5.38" shape="132.69,221.76 131.34,223.78 130.00,224.45"/>
    </edge>
    <edge id=":26_22" function="internal">
        <lane id=":26_22_0" index="0" speed="9.60" length="10.83" width="5.38" shape="128.64,230.86 128.38,231.24 124.06,234.52 119.25,235.64"/>
    </edge>
    <edge id=":26_23" function="internal">
        <lane id=":26_23_0" index="0" speed="4.73" length="3.93" width="5.38" shape="130.00,224.45 128.66,223.78 127.31,221.76"/>
    </edge>
    <edge id=":26_15" function="internal">
        <lane id=":26_15_0" index="0" speed="5.15" length="4.72" width="5.38" shape="119.25,224.87 120.42,224.73 121.26,224.16 121.76,223.17 121.93,221.76"/>
    </edge>
    <edge id=":26_16" function="internal">
        <lane id=":26_16_0" index="0" speed="13.89" length="21.50" width="5.38" shape="119.25,224.87 140.75,224.44"/>
        <lane id=":26_16_1" index="1" speed="13.89" length="21.50" width="5.38" shape="119.25,230.26 140.75,229.82"/>
    </edge>
    <edge id=":26_18" function="internal">
        <lane id=":26_18_0" index="0" speed="9.76" length="20.10" width="5.38" shape="119.25,230.26 123.98,231.55 128.30,234.57 131.47,238.67 132.69,243.26"/>
    </edge>
    <edge id=":26_19" function="internal">
        <lane id=":26_19_0" index="0" speed="4.74" length="7.87" width="5.38" shape="119.25,230.26 121.27,231.60 121.94,232.95 121.27,234.30 119.25,235.64"/>
    </edge>
    <edge id=":27_0" function="internal">
        <lane id=":27_0_0" index="0" speed="5.06" length="4.32" width="5.38" shape="271.93,291.26 271.76,290.09 271.26,289.25 270.42,288.75 269.25,288.58"/>
    </edge>
    <edge id=":27_1" function="internal">
        <lane id=":27_1_0" index="0" speed="13.89" length="21.50" width="5.38" shape="271.93,291.26 271.93,269.76"/>
        <lane id=":27_1_1" index="1" speed="13.89" length="21.50" width="5.38" shape="277.31,291.26 277.31,269.76"/>
    </edge>
    <edge id=":27_3" function="internal">
        <lane id=":27_3_0" index="0" speed="9.76" length="8.96" width="5.38" shape="277.31,291.26 278.53,286.67 281.10,283.34"/>
    </edge>
    <edge id=":27_4" function="internal">
        <lane id=":27_4_0" index="0" speed="4.73" length="3.93" width="5.38" shape="277.31,291.26 278.65,289.24 280.00,288.57"/>
    </edge>
    <edge id=":27_20" function="internal">
        <lane id=":27_20_0" index="0" speed="9.76" length="11.15" width="5.38" shape="281.10,283.34 281.70,282.57 286.02,279.55 290.75,278.26"/>
    </edge>
    <edge id=":27_21" function="internal">
        <lane id=":27_21_0" index="0" speed="4.73" length="3.93" width="5.38" shape="280.00,288.57 281.35,289.24 282.69,291.26"/>
    </edge>
    <edge id=":27_5" function="internal">
        <lane id=":27_5_0" index="0" speed="4.95" length="3.92" width="5.38" shape="290.75,289.03 289.58,289.22 288.74,289.66 288.24,290.34 288.07,291.26"/>
    </edge>
    <edge id=":27_6" function="internal">
        <lane id=":27_6_0" index="0" speed="13.89" length="21.50" width="5.38" shape="290.75,289.03 269.25,288.58"/>
        <lane id=":27_6_1" index="1" speed="13.89" length="21.50" width="5.38" shape="290.75,283.64 269.25,283.20"/>
    </edge>
    <edge id=":27_8" function="internal">
        <lane id=":27_8_0" index="0" speed="9.60" length="20.95" width="5.38" shape="290.75,283.64 285.94,282.52 281.62,279.24 278.51,274.69 277.31,269.76"/>
    </edge>
    <edge id=":27_9" function="internal">
        <lane id=":27_9_0" index="0" speed="4.74" length="7.87" width="5.38" shape="290.75,283.64 288.73,282.30 288.06,280.95 288.73,279.60 290.75,278.26"/>
    </edge>
    <edge id=":27_10" function="internal">
        <lane id=":27_10_0" index="0" speed="5.15" length="4.72" width="5.38" shape="288.07,269.76 288.24,271.17 288.74,272.16 289.58,272.73 290.75,272.87"/>
    </edge>
    <edge id=":27_11" function="internal">
        <lane id=":27_11_0" index="0" speed="13.89" length="21.50" width="5.38" shape="288.07,269.76 288.07,291.26"/>
        <lane id=":27_11_1" index="1" speed="13.89" length="21.50" width="5.38" shape="282.69,269.76 282.69,291.26"/>
    </edge>
    <edge id=":27_13" function="internal">
        <lane id=":27_13_0" index="0" speed="9.68" length="9.54" width="5.38" shape="282.69,269.76 281.48,274.53 278.77,278.27"/>
    </edge>
    <edge id=":27_14" function="internal">
        <lane id=":27_14_0" index="0" speed="4.73" length="3.93" width="5.38" shape="282.69,269.76 281.35,271.78 280.00,272.45"/>
    </edge>
    <edge id=":27_22" function="internal">
        <lane id=":27_22_0" index="0" speed="9.68" length="10.99" width="5.38" shape="278.77,278.27 278.35,278.86 274.02,281.99 269.25,283.20"/>
    </edge>
    <edge id=":27_23" function="internal">
        <lane id=":27_23_0" index="0" speed="4.73" length="3.93" width="5.38" shape="280.00,272.45 278.65,271.78 277.31,269.76"/>
    </edge>
    <edge id=":27_15" function="internal">
        <lane id=":27_15_0" index="0" speed="5.06" length="4.32" width="5.38" shape="269.25,272.44 270.42,272.27 271.26,271.77 271.76,270.93 271.93,269.76"/>
    </edge>
    <edge id=":27_16" function="internal">
        <lane id=":27_16_0" index="0" speed="13.89" length="21.50" width="5.38" shape="269.25,272.44 290.75,272.87"/>
        <lane id=":27_16_1" index="1" speed="13.89" length="21.50" width="5.38" shape="269.25,277.82 290.75,278.26"/>
    </edge>
    <edge id=":27_18" function="internal">
        <lane id=":27_18_0" index="0" speed="9.68" length="20.53" width="5.38" shape="269.25,277.82 274.02,279.03 278.35,282.16 281.48,286.49 282.69,291.26"/>
    </edge>
    <edge id=":27_19" function="internal">
        <lane id=":27_19_0" index="0" speed="4.73" length="7.86" width="5.38" shape="269.25,277.82 271.27,279.17 271.94,280.51 271.27,281.85 269.25,283.20"/>
    </edge>
    <edge id=":28_0" function="internal">
        <lane id=":28_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="251.63,288.58 251.75,288.58"/>
        <lane id=":28_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="251.63,283.20 251.49,283.20"/>
    </edge>
    <edge id=":28_2" function="internal">
        <lane id=":28_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="250.98,272.46 251.63,272.44"/>
        <lane id=":28_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="251.24,277.83 251.63,277.82"/>
    </edge>
    <edge id=":29_0" function="internal">
        <lane id=":29_0_0" index="0" speed="13.89" length="0.10" width="5.38" shape="219.06,321.42 219.06,321.42"/>
        <lane id=":29_0_1" index="1" speed="13.89" length="0.10" width="5.38" shape="213.69,321.15 213.69,321.15"/>
    </edge>
    <edge id=":29_2" function="internal">
        <lane id=":29_2_0" index="0" speed="13.89" length="0.10" width="5.38" shape="202.94,320.60 202.94,320.60"/>
        <lane id=":29_2_1" index="1" speed="13.89" length="0.10" width="5.38" shape="208.31,320.87 208.31,320.87"/>
    </edge>
    <edge id=":30_0" function="internal">
        <lane id=":30_0_0" index="0" speed="13.89" length="1.63" width="5.38" shape="171.45,369.54 170.89,369.57 170.50,369.58 170.11,369.57 169.55,369.54"/>
        <lane id=":30_0_1" index="1" speed="13.89" length="1.63" width="5.38" shape="171.18,364.17 170.78,364.19 170.50,364.19 170.22,364.19 169.82,364.17"/>
    </edge>
    <edge id=":30_2" function="internal">
        <lane id=":30_2_0" index="0" speed="13.89" length="0.54" width="5.38" shape="170.36,353.42 170.44,353.43 170.50,353.43 170.56,353.43 170.64,353.42"/>
        <lane id=":30_2_1" index="1" speed="13.89" length="0.54" width="5.38" shape="170.09,358.80 170.33,358.81 170.50,358.81 170.67,358.81 170.91,358.80"/>
    </edge>
    <edge id=":31_0" function="internal">
        <lane id=":31_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="121.95,321.69 121.93,320.74"/>
        <lane id=":31_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="127.33,321.42 127.31,320.74"/>
    </edge>
    <edge id=":31_2" function="internal">
        <lane id=":31_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="138.07,320.74 138.07,320.87"/>
        <lane id=":31_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="132.69,320.74 132.70,321.15"/>
    </edge>
    <edge id=":32_0" function="internal">
        <lane id=":32_0_0" index="0" speed="13.89" length="8.63" width="5.50" shape="243.84,426.37 241.34,426.09 239.70,425.27 238.07,424.41 235.56,424.01"/>
        <lane id=":32_0_1" index="1" speed="13.89" length="8.63" width="5.50" shape="243.57,421.00 241.14,420.70 239.57,419.83 237.99,418.93 235.56,418.51"/>
    </edge>
    <edge id=":32_2" function="internal">
        <lane id=":32_2_0" index="0" speed="13.89" length="8.22" width="5.38" shape="235.56,413.01 237.85,412.66 239.31,411.83 240.75,410.90 243.02,410.22"/>
        <lane id=":32_2_1" index="1" speed="13.89" length="8.22" width="5.38" shape="235.56,413.01 237.92,413.50 239.45,414.54 240.96,415.46 243.29,415.62"/>
    </edge>
    <edge id=":33_0" function="internal">
        <lane id=":33_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="288.07,377.74 288.05,378.69"/>
        <lane id=":33_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="282.69,377.74 282.67,378.42"/>
    </edge>
    <edge id=":33_2" function="internal">
        <lane id=":33_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="271.93,377.87 271.93,377.74"/>
        <lane id=":33_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="277.30,378.15 277.31,377.74"/>
    </edge>
    <edge id=":34_0" function="internal">
        <lane id=":34_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="348.07,239.74 348.05,240.69"/>
        <lane id=":34_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="342.69,239.74 342.67,240.42"/>
    </edge>
    <edge id=":34_2" function="internal">
        <lane id=":34_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="331.93,239.87 331.93,239.74"/>
        <lane id=":34_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="337.30,240.15 337.31,239.74"/>
    </edge>
    <edge id=":35_0" function="internal">
        <lane id=":35_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="331.93,90.15 331.93,90.27"/>
        <lane id=":35_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="337.31,90.15 337.31,90.01"/>
    </edge>
    <edge id=":35_2" function="internal">
        <lane id=":35_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="348.05,89.48 348.07,90.15"/>
        <lane id=":35_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="342.68,89.74 342.69,90.15"/>
    </edge>
    <edge id=":36_0" function="internal">
        <lane id=":36_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="249.74,8.08 249.86,8.08"/>
        <lane id=":36_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="250.00,2.70 249.86,2.70"/>
    </edge>
    <edge id=":36_2" function="internal">
        <lane id=":36_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="249.86,-8.06 250.53,-8.04"/>
        <lane id=":36_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="249.86,-2.68 250.27,-2.67"/>
    </edge>
    <edge id=":37_0" function="internal">
        <lane id=":37_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="179.25,90.58 179.37,90.58"/>
        <lane id=":37_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="179.51,85.20 179.37,85.20"/>
    </edge>
    <edge id=":37_2" function="internal">
        <lane id=":37_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="179.37,74.44 180.02,74.46"/>
        <lane id=":37_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="179.37,79.82 179.76,79.83"/>
    </edge>
    <edge id=":38_0" function="internal">
        <lane id=":38_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="110.63,90.58 110.75,90.58"/>
        <lane id=":38_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="110.63,85.20 110.49,85.20"/>
    </edge>
    <edge id=":38_2" function="internal">
        <lane id=":38_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="109.98,74.46 110.63,74.44"/>
        <lane id=":38_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="110.24,79.83 110.63,79.82"/>
    </edge>
    <edge id=":39_0" function="internal">
        <lane id=":39_0_0" index="0" speed="13.89" length="0.53" width="5.38" shape="61.93,123.14 61.95,122.49"/>
        <lane id=":39_0_1" index="1" speed="13.89" length="0.53" width="5.38" shape="67.31,123.14 67.32,122.75"/>
    </edge>
    <edge id=":39_2" function="internal">
        <lane id=":39_2_0" index="0" speed="13.89" length="0.13" width="5.38" shape="78.07,123.26 78.07,123.14"/>
        <lane id=":39_2_1" index="1" speed="13.89" length="0.13" width="5.38" shape="72.69,123.00 72.69,123.14"/>
    </edge>
    <edge id=":40_0" function="internal">
        <lane id=":40_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="61.95,192.69 61.93,191.74"/>
        <lane id=":40_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="67.33,192.42 67.31,191.74"/>
    </edge>
    <edge id=":40_2" function="internal">
        <lane id=":40_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="78.07,191.74 78.07,191.87"/>
        <lane id=":40_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="72.69,191.74 72.70,192.15"/>
    </edge>
    <edge id=":41_0" function="internal">
        <lane id=":41_0_0" index="0" speed="13.89" length="0.53" width="5.38" shape="121.93,183.14 121.95,182.49"/>
        <lane id=":41_0_1" index="1" speed="13.89" length="0.53" width="5.38" shape="127.31,183.14 127.32,182.75"/>
    </edge>
    <edge id=":41_2" function="internal">
        <lane id=":41_2_0" index="0" speed="13.89" length="0.13" width="5.38" shape="138.07,183.26 138.07,183.14"/>
        <lane id=":41_2_1" index="1" speed="13.89" length="0.13" width="5.38" shape="132.69,183.00 132.69,183.14"/>
    </edge>
    <edge id=":42_0" function="internal">
        <lane id=":42_0_0" index="0" speed="13.89" length="0.13" width="5.38" shape="170.63,150.58 170.75,150.58"/>
        <lane id=":42_0_1" index="1" speed="13.89" length="0.13" width="5.38" shape="170.63,145.20 170.49,145.20"/>
    </edge>
    <edge id=":42_2" function="internal">
        <lane id=":42_2_0" index="0" speed="13.89" length="0.53" width="5.38" shape="169.98,134.46 170.63,134.44"/>
        <lane id=":42_2_1" index="1" speed="13.89" length="0.53" width="5.38" shape="170.24,139.83 170.63,139.82"/>
    </edge>
    <edge id=":43_0" function="internal">
        <lane id=":43_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="228.07,191.74 228.05,192.69"/>
        <lane id=":43_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="222.69,191.74 222.67,192.42"/>
    </edge>
    <edge id=":43_2" function="internal">
        <lane id=":43_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="211.93,191.87 211.93,191.74"/>
        <lane id=":43_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="217.30,192.15 217.31,191.74"/>
    </edge>
    <edge id=":44_0" function="internal">
        <lane id=":44_0_0" index="0" speed="13.89" length="0.81" width="5.38" shape="180.18,240.56 179.23,240.58"/>
        <lane id=":44_0_1" index="1" speed="13.89" length="0.81" width="5.38" shape="179.91,235.18 179.23,235.20"/>
    </edge>
    <edge id=":44_2" function="internal">
        <lane id=":44_2_0" index="0" speed="13.89" length="0.27" width="5.38" shape="179.23,224.44 179.36,224.44"/>
        <lane id=":44_2_1" index="1" speed="13.89" length="0.27" width="5.38" shape="179.23,229.82 179.64,229.81"/>
    </edge>
    <edge id=":45_0" function="internal">
        <lane id=":45_0_0" index="0" speed="13.89" length="0.27" width="5.38" shape="271.93,183.28 271.93,183.15"/>
        <lane id=":45_0_1" index="1" speed="13.89" length="0.27" width="5.38" shape="277.31,183.28 277.30,182.87"/>
    </edge>
    <edge id=":45_2" function="internal">
        <lane id=":45_2_0" index="0" speed="13.89" length="0.81" width="5.38" shape="288.05,182.33 288.07,183.28"/>
        <lane id=":45_2_1" index="1" speed="13.89" length="0.81" width="5.38" shape="282.67,182.60 282.69,183.28"/>
    </edge>
    <edge id=":Custom22_0" function="internal">
        <lane id=":Custom22_0_0" index="0" speed="4.73" length="7.86" width="5.38" shape="-0.00,2.69 -2.02,1.34 -2.69,-0.00 -2.02,-1.35 0.00,-2.69"/>
    </edge>
    <edge id=":Custom23_0" function="internal">
        <lane id=":Custom23_0_0" index="0" speed="4.79" length="8.03" width="5.50" shape="39.50,418.51 37.44,417.13 36.75,415.76 37.44,414.38 39.50,413.01"/>
    </edge>

    <edge id="-0" from="26" to="40" priority="-1" shape="110.50,232.51 106.53,232.31 102.60,231.73 98.74,230.76 95.00,229.43 91.41,227.73 88.00,225.68 84.81,223.32 81.86,220.65 79.19,217.70 76.83,214.51 74.78,211.10 73.08,207.51 71.74,203.76 70.78,199.91 70.20,195.98 70.00,192.01">
        <lane id="-0_0" index="0" speed="13.89" length="80.27" width="5.38" shape="119.25,241.03 105.74,240.35 101.02,239.65 96.40,238.49 91.91,236.90 87.59,234.85 83.51,232.40 79.69,229.57 76.15,226.36 72.94,222.82 70.11,219.00 67.66,214.92 65.62,210.60 64.01,206.10 62.85,201.48 62.16,196.77 61.95,192.69"/>
        <lane id="-0_1" index="1" speed="13.89" length="80.27" width="5.38" shape="119.25,235.64 106.27,234.99 102.07,234.37 97.96,233.34 93.97,231.92 90.14,230.10 86.50,227.92 83.10,225.40 79.96,222.55 77.11,219.41 74.59,216.01 72.41,212.37 70.59,208.54 69.16,204.54 68.14,200.43 67.52,196.24 67.33,192.42"/>
    </edge>
    <edge id="-1" from="39" to="40" priority="-1">
        <lane id="-1_0" index="0" speed="13.89" length="68.60" width="5.38" shape="78.07,123.14 78.07,191.74"/>
        <lane id="-1_1" index="1" speed="13.89" length="68.60" width="5.38" shape="72.69,123.14 72.69,191.74"/>
    </edge>
    <edge id="-10" from="25" to="45" priority="-1" shape="239.50,142.51 243.47,142.70 247.40,143.29 251.26,144.25 255.00,145.59 258.59,147.29 262.00,149.33 265.19,151.70 268.14,154.37 270.81,157.32 273.17,160.51 275.22,163.92 276.92,167.51 278.26,171.25 279.22,175.11 279.80,179.04 280.00,183.01">
        <lane id="-10_0" index="0" speed="13.89" length="80.27" width="5.38" shape="230.75,134.01 244.26,134.66 248.98,135.37 253.60,136.52 258.10,138.13 262.40,140.16 266.49,142.61 270.32,145.45 273.85,148.66 277.06,152.20 279.89,156.02 282.34,160.10 284.38,164.41 285.99,168.91 287.15,173.54 287.84,178.25 288.05,182.33"/>
        <lane id="-10_1" index="1" speed="13.89" length="80.27" width="5.38" shape="230.75,139.40 243.73,140.02 247.93,140.65 252.04,141.67 256.03,143.10 259.86,144.91 263.50,147.09 266.90,149.62 270.04,152.47 272.89,155.61 275.41,159.01 277.59,162.65 279.41,166.48 280.84,170.47 281.86,174.59 282.48,178.78 282.67,182.60"/>
    </edge>
    <edge id="-11" from="36" to="35" priority="-1" shape="250.00,0.01 258.82,0.44 267.56,1.74 276.13,3.88 284.44,6.86 292.43,10.63 300.00,15.18 307.09,20.44 313.64,26.37 319.57,32.91 324.83,40.01 329.37,47.58 333.15,55.57 336.12,63.88 338.27,72.45 339.57,81.19 340.00,90.01">
        <lane id="-11_0" index="0" speed="13.89" length="148.98" width="5.38" shape="250.53,-8.04 259.61,-7.60 269.13,-6.18 278.48,-3.85 287.53,-0.60 296.24,3.50 304.49,8.47 312.21,14.19 319.35,20.65 325.82,27.79 331.55,35.52 336.50,43.77 340.62,52.48 343.85,61.53 346.19,70.87 347.61,80.40 348.05,89.48"/>
        <lane id="-11_1" index="1" speed="13.89" length="148.98" width="5.38" shape="250.27,-2.67 259.08,-2.24 268.08,-0.90 276.91,1.30 285.47,4.37 293.70,8.25 301.50,12.94 308.80,18.36 315.54,24.46 321.65,31.20 327.07,38.51 331.75,46.31 335.64,54.54 338.70,63.10 340.91,71.92 342.25,80.93 342.68,89.74"/>
    </edge>
    <edge id="-12" from="28" to="27" priority="-1" shape="251.50,280.51 260.50,280.51">
        <lane id="-12_0" index="0" speed="13.89" length="17.62" width="5.38" shape="251.63,272.44 269.25,272.44"/>
        <lane id="-12_1" index="1" speed="13.89" length="17.62" width="5.38" shape="251.63,277.82 269.25,277.82"/>
    </edge>
    <edge id="-13" from="Custom22" to="36" priority="-1">
        <lane id="-13_0" index="0" speed="13.89" length="249.86" width="5.38" shape="0.00,-8.07 249.86,-8.06"/>
        <lane id="-13_1" index="1" speed="13.89" length="249.86" width="5.38" shape="0.00,-2.69 249.86,-2.68"/>
    </edge>
    <edge id="-14" from="35" to="34" priority="-1">
        <lane id="-14_0" index="0" speed="13.89" length="149.59" width="5.38" shape="348.07,90.15 348.07,239.74"/>
        <lane id="-14_1" index="1" speed="13.89" length="149.59" width="5.38" shape="342.69,90.15 342.69,239.74"/>
    </edge>
    <edge id="-15" from="Custom23" to="32" priority="-1" shape="39.50,415.76 239.50,415.76">
        <lane id="-15_0" index="0" speed="13.89" length="196.06" width="5.50" shape="39.50,413.01 235.56,413.01"/>
    </edge>
    <edge id="-16" from="33" to="27" priority="-1" shape="280.00,378.01 280.00,300.01">
        <lane id="-16_0" index="0" speed="13.89" length="86.48" width="5.38" shape="271.93,377.74 271.93,291.26"/>
        <lane id="-16_1" index="1" speed="13.89" length="86.48" width="5.38" shape="277.31,377.74 277.31,291.26"/>
    </edge>
    <edge id="-17" from="27" to="45" priority="-1" shape="280.00,261.01 280.00,183.01">
        <lane id="-17_0" index="0" speed="13.89" length="86.48" width="5.38" shape="271.93,269.76 271.93,183.28"/>
        <lane id="-17_1" index="1" speed="13.89" length="86.48" width="5.38" shape="277.31,269.76 277.31,183.28"/>
    </edge>
    <edge id="-18" from="29" to="28" priority="-1" shape="211.00,321.01 211.20,317.04 211.78,313.11 212.74,309.25 214.08,305.51 215.78,301.92 217.83,298.51 220.19,295.31 222.86,292.37 225.81,289.70 229.00,287.33 232.41,285.29 236.00,283.59 239.74,282.25 243.60,281.29 247.53,280.70 251.50,280.51">
        <lane id="-18_0" index="0" speed="13.89" length="71.39" width="5.38" shape="202.94,320.60 203.16,316.25 203.85,311.54 205.01,306.91 206.62,302.41 208.66,298.10 211.11,294.03 213.94,290.19 217.15,286.65 220.68,283.45 224.51,280.61 228.60,278.16 232.90,276.13 237.40,274.52 242.02,273.37 246.74,272.66 250.98,272.46"/>
        <lane id="-18_1" index="1" speed="13.89" length="71.39" width="5.38" shape="208.31,320.87 208.52,316.78 209.14,312.59 210.16,308.47 211.59,304.48 213.41,300.65 215.59,297.02 218.11,293.60 220.96,290.46 224.10,287.62 227.50,285.09 231.14,282.91 234.97,281.10 238.96,279.67 243.07,278.65 247.27,278.02 251.24,277.83"/>
    </edge>
    <edge id="-19" from="33" to="32" priority="-1" shape="280.00,378.01 279.80,381.98 279.22,385.91 278.26,389.76 276.92,393.51 275.22,397.10 273.17,400.51 270.81,403.70 268.14,406.65 265.19,409.32 262.00,411.68 258.59,413.73 255.00,415.43 251.26,416.76 247.40,417.73 243.47,418.31 239.50,418.51">
        <lane id="-19_0" index="0" speed="13.89" length="67.30" width="5.38" shape="288.05,378.69 287.84,382.77 287.15,387.48 285.99,392.10 284.38,396.60 282.34,400.92 279.89,405.00 277.06,408.82 273.85,412.36 270.31,415.57 266.49,418.40 262.41,420.85 258.09,422.90 253.60,424.49 248.98,425.65 244.26,426.35 243.84,426.37"/>
        <lane id="-19_1" index="1" speed="13.89" length="67.30" width="5.38" shape="282.67,378.42 282.48,382.24 281.86,386.43 280.84,390.54 279.41,394.54 277.59,398.37 275.41,402.01 272.89,405.41 270.04,408.55 266.90,411.40 263.50,413.92 259.86,416.10 256.03,417.92 252.04,419.34 247.93,420.37 243.73,420.99 243.57,421.00"/>
    </edge>
    <edge id="-2" from="39" to="38" priority="-1" shape="70.00,123.01 70.19,119.04 70.78,115.11 71.74,111.25 73.08,107.51 74.78,103.92 76.82,100.51 79.19,97.32 81.86,94.37 84.81,91.70 88.00,89.33 91.41,87.29 95.00,85.59 98.74,84.25 102.60,83.29 106.53,82.70 110.50,82.51">
        <lane id="-2_0" index="0" speed="13.89" length="71.27" width="5.38" shape="61.95,122.49 62.15,118.25 62.86,113.53 64.01,108.91 65.62,104.41 67.65,100.11 70.10,96.02 72.94,92.19 76.15,88.66 79.68,85.45 83.51,82.61 87.60,80.16 91.90,78.13 96.40,76.52 101.02,75.37 105.74,74.66 109.98,74.46"/>
        <lane id="-2_1" index="1" speed="13.89" length="71.27" width="5.38" shape="67.32,122.75 67.51,118.78 68.14,114.58 69.16,110.47 70.59,106.48 72.40,102.65 74.58,99.01 77.11,95.61 79.96,92.47 83.10,89.62 86.50,87.09 90.14,84.91 93.97,83.10 97.96,81.67 102.07,80.65 106.27,80.02 110.24,79.83"/>
    </edge>
    <edge id="-20" from="34" to="27" priority="-1" shape="340.00,240.01 339.80,243.98 339.22,247.91 338.26,251.76 336.92,255.51 335.22,259.10 333.17,262.51 330.81,265.70 328.14,268.65 325.19,271.32 322.00,273.68 318.59,275.73 315.00,277.43 311.26,278.76 307.40,279.73 303.47,280.31 299.50,280.51">
        <lane id="-20_0" index="0" speed="13.89" length="80.27" width="5.38" shape="348.05,240.69 347.84,244.77 347.15,249.48 345.99,254.10 344.38,258.60 342.34,262.92 339.89,267.00 337.06,270.82 333.85,274.36 330.31,277.57 326.49,280.40 322.41,282.85 318.09,284.90 313.60,286.49 308.98,287.65 304.26,288.35 290.75,289.03"/>
        <lane id="-20_1" index="1" speed="13.89" length="80.27" width="5.38" shape="342.67,240.42 342.48,244.24 341.86,248.43 340.84,252.54 339.41,256.54 337.59,260.37 335.41,264.01 332.89,267.41 330.04,270.55 326.90,273.40 323.50,275.92 319.86,278.10 316.03,279.92 312.04,281.34 307.93,282.37 303.73,282.99 290.75,283.64"/>
    </edge>
    <edge id="-21" from="29" to="30" priority="-1" shape="211.00,321.01 210.80,324.98 210.22,328.91 209.26,332.76 207.92,336.51 206.22,340.10 204.17,343.51 201.81,346.70 199.14,349.65 196.19,352.32 193.00,354.68 189.59,356.73 186.00,358.43 182.26,359.76 178.40,360.73 174.47,361.31 170.50,361.51">
        <lane id="-21_0" index="0" speed="13.89" length="70.96" width="5.38" shape="219.06,321.42 218.84,325.77 218.15,330.48 216.99,335.10 215.38,339.60 213.34,343.92 210.89,348.00 208.06,351.82 204.85,355.36 201.31,358.57 197.49,361.40 193.41,363.85 189.09,365.90 184.60,367.49 179.98,368.65 175.26,369.35 171.45,369.54"/>
        <lane id="-21_1" index="1" speed="13.89" length="70.96" width="5.38" shape="213.69,321.15 213.48,325.24 212.86,329.43 211.84,333.54 210.41,337.54 208.59,341.37 206.41,345.01 203.89,348.41 201.04,351.55 197.90,354.40 194.50,356.92 190.86,359.10 187.03,360.92 183.04,362.34 178.93,363.37 174.73,363.99 171.18,364.17"/>
    </edge>
    <edge id="-22" from="30" to="31" priority="-1" shape="170.50,361.51 166.53,361.31 162.60,360.73 158.74,359.76 155.00,358.43 151.41,356.73 148.00,354.68 144.81,352.32 141.86,349.65 139.19,346.70 136.83,343.51 134.78,340.10 133.08,336.51 131.74,332.76 130.78,328.91 130.20,324.98 130.00,321.01">
        <lane id="-22_0" index="0" speed="13.89" length="70.69" width="5.38" shape="169.55,369.54 165.74,369.35 161.02,368.65 156.40,367.49 151.91,365.90 147.59,363.85 143.51,361.40 139.69,358.57 136.15,355.36 132.94,351.82 130.11,348.00 127.66,343.92 125.62,339.60 124.01,335.10 122.85,330.48 122.16,325.77 121.95,321.69"/>
        <lane id="-22_1" index="1" speed="13.89" length="70.69" width="5.38" shape="169.82,364.17 166.27,363.99 162.07,363.37 157.96,362.34 153.97,360.92 150.14,359.10 146.50,356.92 143.10,354.40 139.96,351.55 137.11,348.41 134.59,345.01 132.41,341.37 130.59,337.54 129.16,333.54 128.14,329.43 127.52,325.24 127.33,321.42"/>
    </edge>
    <edge id="-23" from="41" to="26" priority="-1" shape="130.00,183.01 130.00,213.01">
        <lane id="-23_0" index="0" speed="13.89" length="38.62" width="5.38" shape="138.07,183.14 138.07,221.76"/>
        <lane id="-23_1" index="1" speed="13.89" length="38.62" width="5.38" shape="132.69,183.14 132.69,221.76"/>
    </edge>
    <edge id="-24" from="31" to="26" priority="-1" shape="130.00,321.01 130.00,252.01">
        <lane id="-24_0" index="0" speed="13.89" length="77.48" width="5.38" shape="121.93,320.74 121.93,243.26"/>
        <lane id="-24_1" index="1" speed="13.89" length="77.48" width="5.38" shape="127.31,320.74 127.31,243.26"/>
    </edge>
    <edge id="-3" from="37" to="38" priority="-1">
        <lane id="-3_0" index="0" speed="13.89" length="68.73" width="5.38" shape="179.37,90.58 110.63,90.58"/>
        <lane id="-3_1" index="1" speed="13.89" length="68.73" width="5.38" shape="179.37,85.20 110.63,85.20"/>
    </edge>
    <edge id="-4" from="37" to="25" priority="-1" shape="179.50,82.51 183.47,82.70 187.40,83.29 191.26,84.25 195.00,85.59 198.59,87.29 202.00,89.33 205.19,91.70 208.14,94.37 210.81,97.32 213.17,100.51 215.22,103.92 216.92,107.51 218.26,111.25 219.22,115.11 219.80,119.04 220.00,123.01">
        <lane id="-4_0" index="0" speed="13.89" length="80.42" width="5.38" shape="180.02,74.46 184.26,74.66 188.98,75.37 193.60,76.52 198.10,78.13 202.40,80.16 206.49,82.61 210.32,85.45 213.85,88.66 217.06,92.20 219.89,96.02 222.34,100.10 224.38,104.41 225.99,108.91 227.15,113.54 227.84,118.25 228.52,131.76"/>
        <lane id="-4_1" index="1" speed="13.89" length="80.42" width="5.38" shape="179.76,79.83 183.73,80.02 187.93,80.65 192.04,81.67 196.03,83.10 199.86,84.91 203.50,87.09 206.90,89.62 210.04,92.47 212.89,95.61 215.41,99.01 217.59,102.65 219.41,106.48 220.84,110.47 221.86,114.59 222.48,118.78 223.13,131.76"/>
    </edge>
    <edge id="-5" from="25" to="43" priority="-1" shape="220.00,162.01 220.00,192.01">
        <lane id="-5_0" index="0" speed="13.89" length="38.48" width="5.38" shape="228.07,153.26 228.07,191.74"/>
        <lane id="-5_1" index="1" speed="13.89" length="38.48" width="5.38" shape="222.69,153.26 222.69,191.74"/>
    </edge>
    <edge id="-6" from="43" to="44" priority="-1" shape="220.00,192.01 219.80,195.98 219.22,199.91 218.26,203.76 216.92,207.51 215.22,211.10 213.17,214.51 210.81,217.70 208.14,220.65 205.19,223.32 202.00,225.68 198.59,227.73 195.00,229.43 191.26,230.76 187.40,231.73 183.47,232.31 179.50,232.51">
        <lane id="-6_0" index="0" speed="13.89" length="70.96" width="5.38" shape="228.05,192.69 227.84,196.77 227.15,201.48 225.99,206.10 224.38,210.60 222.34,214.92 219.89,219.00 217.06,222.82 213.85,226.36 210.31,229.57 206.49,232.40 202.41,234.85 198.09,236.90 193.60,238.49 188.98,239.65 184.26,240.35 180.18,240.56"/>
        <lane id="-6_1" index="1" speed="13.89" length="70.96" width="5.38" shape="222.67,192.42 222.48,196.24 221.86,200.43 220.84,204.54 219.41,208.54 217.59,212.37 215.41,216.01 212.89,219.41 210.04,222.55 206.90,225.40 203.50,227.92 199.86,230.10 196.03,231.92 192.04,233.34 187.93,234.37 183.73,234.99 179.91,235.18"/>
    </edge>
    <edge id="-7" from="44" to="26" priority="-1" shape="179.50,232.51 149.50,232.51">
        <lane id="-7_0" index="0" speed="13.89" length="38.48" width="5.38" shape="179.23,240.58 140.75,240.58"/>
        <lane id="-7_1" index="1" speed="13.89" length="38.48" width="5.38" shape="179.23,235.20 140.75,235.20"/>
    </edge>
    <edge id="-8" from="41" to="42" priority="-1" shape="130.00,183.01 130.19,179.04 130.78,175.11 131.74,171.25 133.08,167.51 134.78,163.92 136.82,160.51 139.19,157.32 141.86,154.37 144.81,151.70 148.00,149.33 151.41,147.29 155.00,145.59 158.74,144.25 162.60,143.29 166.53,142.70 170.50,142.51">
        <lane id="-8_0" index="0" speed="13.89" length="71.27" width="5.38" shape="121.95,182.49 122.15,178.25 122.86,173.53 124.01,168.91 125.62,164.41 127.65,160.11 130.10,156.02 132.94,152.19 136.15,148.66 139.68,145.45 143.51,142.61 147.60,140.16 151.90,138.13 156.40,136.52 161.02,135.37 165.74,134.66 169.98,134.46"/>
        <lane id="-8_1" index="1" speed="13.89" length="71.27" width="5.38" shape="127.32,182.75 127.51,178.78 128.14,174.58 129.16,170.47 130.59,166.48 132.40,162.65 134.58,159.01 137.11,155.61 139.96,152.47 143.10,149.62 146.50,147.09 150.14,144.91 153.97,143.10 157.96,141.67 162.07,140.65 166.27,140.02 170.24,139.83"/>
    </edge>
    <edge id="-9" from="25" to="42" priority="-1" shape="200.50,142.51 170.50,142.51">
        <lane id="-9_0" index="0" speed="13.89" length="38.62" width="5.38" shape="209.25,150.58 170.63,150.58"/>
        <lane id="-9_1" index="1" speed="13.89" length="38.62" width="5.38" shape="209.25,145.20 170.63,145.20"/>
    </edge>
    <edge id="0" from="40" to="26" priority="-1" shape="70.00,192.01 70.20,195.98 70.78,199.91 71.74,203.76 73.08,207.51 74.78,211.10 76.83,214.51 79.19,217.70 81.86,220.65 84.81,223.32 88.00,225.68 91.41,227.73 95.00,229.43 98.74,230.76 102.60,231.73 106.53,232.31 110.50,232.51">
        <lane id="0_0" index="0" speed="13.89" length="63.89" width="5.38" shape="78.07,191.87 78.24,195.19 78.71,198.34 79.47,201.42 80.54,204.42 81.90,207.28 83.55,210.02 85.44,212.58 87.57,214.94 89.93,217.07 92.49,218.96 95.23,220.61 98.09,221.96 101.08,223.03 104.18,223.81 107.32,224.27 119.25,224.87"/>
        <lane id="0_1" index="1" speed="13.89" length="63.89" width="5.38" shape="72.70,192.15 72.88,195.72 73.42,199.39 74.32,202.98 75.57,206.48 77.15,209.83 79.07,213.01 81.27,215.99 83.76,218.75 86.52,221.24 89.50,223.44 92.68,225.36 96.03,226.94 99.52,228.18 103.13,229.09 106.79,229.63 119.25,230.26"/>
    </edge>
    <edge id="1" from="40" to="39" priority="-1">
        <lane id="1_0" index="0" speed="13.89" length="68.60" width="5.38" shape="61.93,191.74 61.93,123.14"/>
        <lane id="1_1" index="1" speed="13.89" length="68.60" width="5.38" shape="67.31,191.74 67.31,123.14"/>
    </edge>
    <edge id="10" from="45" to="25" priority="-1" shape="280.00,183.01 279.80,179.04 279.22,175.11 278.26,171.25 276.92,167.51 275.22,163.92 273.17,160.51 270.81,157.32 268.14,154.37 265.19,151.70 262.00,149.33 258.59,147.29 255.00,145.59 251.26,144.25 247.40,143.29 243.47,142.70 239.50,142.51">
        <lane id="10_0" index="0" speed="13.89" length="63.90" width="5.38" shape="271.93,183.15 271.76,179.83 271.29,176.68 270.53,173.59 269.46,170.61 268.10,167.74 266.45,165.00 264.56,162.44 262.43,160.08 260.06,157.95 257.51,156.05 254.78,154.42 251.90,153.05 248.92,151.98 245.82,151.21 242.68,150.74 230.75,150.17"/>
        <lane id="10_1" index="1" speed="13.89" length="63.90" width="5.38" shape="277.30,182.87 277.12,179.30 276.58,175.63 275.68,172.03 274.43,168.54 272.85,165.19 270.93,162.01 268.73,159.03 266.24,156.27 263.48,153.78 260.50,151.57 257.32,149.67 253.97,148.08 250.48,146.83 246.87,145.93 243.21,145.38 230.75,144.78"/>
    </edge>
    <edge id="11" from="35" to="36" priority="-1" shape="340.00,90.01 339.57,81.19 338.27,72.45 336.12,63.88 333.15,55.57 329.37,47.58 324.83,40.01 319.57,32.91 313.64,26.37 307.09,20.44 300.00,15.18 292.43,10.63 284.44,6.86 276.13,3.88 267.56,1.74 258.82,0.44 250.00,0.01">
        <lane id="11_0" index="0" speed="13.89" length="133.11" width="5.38" shape="331.93,90.27 331.53,81.98 330.35,74.03 328.39,66.23 325.68,58.66 322.24,51.39 318.11,44.50 313.32,38.03 307.93,32.09 301.97,26.69 295.51,21.89 288.62,17.76 281.35,14.32 273.78,11.61 265.99,9.66 258.03,8.48 249.74,8.08"/>
        <lane id="11_1" index="1" speed="13.89" length="133.11" width="5.38" shape="337.31,90.01 336.89,81.45 335.63,72.98 333.54,64.66 330.66,56.60 326.99,48.85 322.59,41.51 317.49,34.62 311.74,28.28 305.38,22.52 298.50,17.42 291.16,13.01 283.41,9.35 275.35,6.46 267.04,4.38 258.56,3.12 250.00,2.70"/>
    </edge>
    <edge id="12" from="27" to="28" priority="-1" shape="260.50,280.51 251.50,280.51">
        <lane id="12_0" index="0" speed="13.89" length="17.62" width="5.38" shape="269.25,288.58 251.63,288.58"/>
        <lane id="12_1" index="1" speed="13.89" length="17.62" width="5.38" shape="269.25,283.20 251.63,283.20"/>
    </edge>
    <edge id="13" from="36" to="Custom22" priority="-1">
        <lane id="13_0" index="0" speed="13.89" length="249.86" width="5.38" shape="249.86,8.08 -0.00,8.07"/>
        <lane id="13_1" index="1" speed="13.89" length="249.86" width="5.38" shape="249.86,2.70 -0.00,2.69"/>
    </edge>
    <edge id="14" from="34" to="35" priority="-1">
        <lane id="14_0" index="0" speed="13.89" length="149.59" width="5.38" shape="331.93,239.74 331.93,90.15"/>
        <lane id="14_1" index="1" speed="13.89" length="149.59" width="5.38" shape="337.31,239.74 337.31,90.15"/>
    </edge>
    <edge id="15" from="32" to="Custom23" priority="-1" shape="239.50,415.76 39.50,415.76">
        <lane id="15_0" index="0" speed="13.89" length="196.06" width="5.50" shape="235.56,424.01 39.50,424.01"/>
        <lane id="15_1" index="1" speed="13.89" length="196.06" width="5.50" shape="235.56,418.51 39.50,418.51"/>
    </edge>
    <edge id="16" from="27" to="33" priority="-1" shape="280.00,300.01 280.00,378.01">
        <lane id="16_0" index="0" speed="13.89" length="86.48" width="5.38" shape="288.07,291.26 288.07,377.74"/>
        <lane id="16_1" index="1" speed="13.89" length="86.48" width="5.38" shape="282.69,291.26 282.69,377.74"/>
    </edge>
    <edge id="17" from="45" to="27" priority="-1" shape="280.00,183.01 280.00,261.01">
        <lane id="17_0" index="0" speed="13.89" length="86.48" width="5.38" shape="288.07,183.28 288.07,269.76"/>
        <lane id="17_1" index="1" speed="13.89" length="86.48" width="5.38" shape="282.69,183.28 282.69,269.76"/>
    </edge>
    <edge id="18" from="28" to="29" priority="-1" shape="251.50,280.51 247.53,280.70 243.60,281.29 239.74,282.25 236.00,283.59 232.41,285.29 229.00,287.33 225.81,289.70 222.86,292.37 220.19,295.31 217.83,298.51 215.78,301.92 214.08,305.51 212.74,309.25 211.78,313.11 211.20,317.04 211.00,321.01">
        <lane id="18_0" index="0" speed="13.89" length="55.53" width="5.38" shape="251.75,288.58 248.32,288.74 245.18,289.21 242.08,289.98 239.10,291.05 236.22,292.42 233.49,294.05 230.94,295.95 228.57,298.09 226.44,300.43 224.55,302.99 222.90,305.74 221.54,308.61 220.47,311.59 219.71,314.68 219.24,317.83 219.06,321.42"/>
        <lane id="18_1" index="1" speed="13.89" length="55.53" width="5.38" shape="251.49,283.20 247.79,283.38 244.13,283.93 240.52,284.83 237.03,286.08 233.68,287.67 230.50,289.57 227.52,291.78 224.76,294.28 222.27,297.02 220.07,300.00 218.15,303.19 216.57,306.54 215.32,310.03 214.42,313.63 213.88,317.30 213.69,321.15"/>
    </edge>
    <edge id="19" from="32" to="33" priority="-1" shape="239.50,418.51 243.47,418.31 247.40,417.73 251.26,416.76 255.00,415.43 258.59,413.73 262.00,411.68 265.19,409.32 268.14,406.65 270.81,403.70 273.17,400.51 275.22,397.10 276.92,393.51 278.26,389.76 279.22,385.91 279.80,381.98 280.00,378.01">
        <lane id="19_0" index="0" speed="13.89" length="51.47" width="5.38" shape="243.02,410.22 245.82,409.81 248.92,409.03 251.91,407.96 254.77,406.61 257.51,404.96 260.07,403.07 262.43,400.94 264.56,398.58 266.45,396.02 268.10,393.28 269.46,390.42 270.53,387.42 271.29,384.34 271.76,381.19 271.93,377.87"/>
        <lane id="19_1" index="1" speed="13.89" length="51.47" width="5.38" shape="243.29,415.62 246.87,415.09 250.48,414.18 253.97,412.94 257.32,411.36 260.50,409.44 263.48,407.24 266.24,404.75 268.73,401.99 270.93,399.01 272.85,395.83 274.43,392.48 275.68,388.98 276.58,385.39 277.12,381.72 277.30,378.15"/>
    </edge>
    <edge id="2" from="38" to="39" priority="-1" shape="110.50,82.51 106.53,82.70 102.60,83.29 98.74,84.25 95.00,85.59 91.41,87.29 88.00,89.33 84.81,91.70 81.86,94.37 79.19,97.32 76.82,100.51 74.78,103.92 73.08,107.51 71.74,111.25 70.78,115.11 70.19,119.04 70.00,123.01">
        <lane id="2_0" index="0" speed="13.89" length="55.38" width="5.38" shape="110.75,90.58 107.32,90.74 104.18,91.21 101.08,91.98 98.10,93.05 95.22,94.42 92.49,96.05 89.94,97.95 87.57,100.08 85.44,102.45 83.54,105.00 81.91,107.73 80.54,110.61 79.47,113.59 78.70,116.69 78.23,119.83 78.07,123.26"/>
        <lane id="2_1" index="1" speed="13.89" length="55.38" width="5.38" shape="110.49,85.20 106.79,85.38 103.13,85.93 99.52,86.83 96.03,88.08 92.68,89.67 89.50,91.57 86.52,93.78 83.76,96.27 81.27,99.03 79.06,102.01 77.16,105.19 75.57,108.54 74.32,112.03 73.42,115.64 72.87,119.30 72.69,123.00"/>
    </edge>
    <edge id="20" from="27" to="34" priority="-1" shape="299.50,280.51 303.47,280.31 307.40,279.73 311.26,278.76 315.00,277.43 318.59,275.73 322.00,273.68 325.19,271.32 328.14,268.65 330.81,265.70 333.17,262.51 335.22,259.10 336.92,255.51 338.26,251.76 339.22,247.91 339.80,243.98 340.00,240.01">
        <lane id="20_0" index="0" speed="13.89" length="63.89" width="5.38" shape="290.75,272.87 302.68,272.27 305.82,271.81 308.92,271.03 311.91,269.96 314.77,268.61 317.51,266.96 320.07,265.07 322.43,262.94 324.56,260.58 326.45,258.02 328.10,255.28 329.46,252.42 330.53,249.42 331.29,246.34 331.76,243.19 331.93,239.87"/>
        <lane id="20_1" index="1" speed="13.89" length="63.89" width="5.38" shape="290.75,278.26 303.21,277.63 306.87,277.09 310.48,276.18 313.97,274.94 317.32,273.36 320.50,271.44 323.48,269.24 326.24,266.75 328.73,263.99 330.93,261.01 332.85,257.83 334.43,254.48 335.68,250.98 336.58,247.39 337.12,243.72 337.30,240.15"/>
    </edge>
    <edge id="21" from="30" to="29" priority="-1" shape="170.50,361.51 174.47,361.31 178.40,360.73 182.26,359.76 186.00,358.43 189.59,356.73 193.00,354.68 196.19,352.32 199.14,349.65 201.81,346.70 204.17,343.51 206.22,340.10 207.92,336.51 209.26,332.76 210.22,328.91 210.80,324.98 211.00,321.01">
        <lane id="21_0" index="0" speed="13.89" length="55.13" width="5.38" shape="170.64,353.42 173.68,353.27 176.82,352.81 179.92,352.03 182.91,350.96 185.77,349.61 188.51,347.96 191.07,346.07 193.43,343.94 195.56,341.58 197.45,339.02 199.10,336.28 200.46,333.42 201.53,330.42 202.29,327.34 202.76,324.19 202.94,320.60"/>
        <lane id="21_1" index="1" speed="13.89" length="55.13" width="5.38" shape="170.91,358.80 174.21,358.63 177.87,358.09 181.48,357.18 184.97,355.94 188.32,354.36 191.50,352.44 194.48,350.24 197.24,347.75 199.73,344.99 201.93,342.01 203.85,338.83 205.43,335.48 206.68,331.98 207.58,328.39 208.12,324.72 208.31,320.87"/>
    </edge>
    <edge id="22" from="31" to="30" priority="-1" shape="130.00,321.01 130.20,324.98 130.78,328.91 131.74,332.76 133.08,336.51 134.78,340.10 136.83,343.51 139.19,346.70 141.86,349.65 144.81,352.32 148.00,354.68 151.41,356.73 155.00,358.43 158.74,359.76 162.60,360.73 166.53,361.31 170.50,361.51">
        <lane id="22_0" index="0" speed="13.89" length="54.86" width="5.38" shape="138.07,320.87 138.24,324.19 138.71,327.34 139.47,330.42 140.54,333.42 141.90,336.28 143.55,339.02 145.44,341.58 147.57,343.94 149.93,346.07 152.49,347.96 155.23,349.61 158.09,350.96 161.08,352.03 164.18,352.81 167.32,353.27 170.36,353.42"/>
        <lane id="22_1" index="1" speed="13.89" length="54.86" width="5.38" shape="132.70,321.15 132.88,324.72 133.42,328.39 134.32,331.98 135.57,335.48 137.15,338.83 139.07,342.01 141.27,344.99 143.76,347.75 146.52,350.24 149.50,352.44 152.68,354.36 156.03,355.94 159.52,357.18 163.13,358.09 166.79,358.63 170.09,358.80"/>
    </edge>
    <edge id="23" from="26" to="41" priority="-1" shape="130.00,213.01 130.00,183.01">
        <lane id="23_0" index="0" speed="13.89" length="38.62" width="5.38" shape="121.93,221.76 121.93,183.14"/>
        <lane id="23_1" index="1" speed="13.89" length="38.62" width="5.38" shape="127.31,221.76 127.31,183.14"/>
    </edge>
    <edge id="24" from="26" to="31" priority="-1" shape="130.00,252.01 130.00,321.01">
        <lane id="24_0" index="0" speed="13.89" length="77.48" width="5.38" shape="138.07,243.26 138.07,320.74"/>
        <lane id="24_1" index="1" speed="13.89" length="77.48" width="5.38" shape="132.69,243.26 132.69,320.74"/>
    </edge>
    <edge id="3" from="38" to="37" priority="-1">
        <lane id="3_0" index="0" speed="13.89" length="68.73" width="5.38" shape="110.63,74.44 179.37,74.44"/>
        <lane id="3_1" index="1" speed="13.89" length="68.73" width="5.38" shape="110.63,79.82 179.37,79.82"/>
    </edge>
    <edge id="4" from="25" to="37" priority="-1" shape="220.00,123.01 219.80,119.04 219.22,115.11 218.26,111.25 216.92,107.51 215.22,103.92 213.17,100.51 210.81,97.32 208.14,94.37 205.19,91.70 202.00,89.33 198.59,87.29 195.00,85.59 191.26,84.25 187.40,83.29 183.47,82.70 179.50,82.51">
        <lane id="4_0" index="0" speed="13.89" length="64.02" width="5.38" shape="212.36,131.76 211.76,119.83 211.29,116.68 210.53,113.59 209.46,110.61 208.10,107.74 206.45,105.00 204.56,102.44 202.43,100.08 200.06,97.95 197.51,96.05 194.78,94.42 191.90,93.05 188.92,91.98 185.82,91.21 182.68,90.74 179.25,90.58"/>
        <lane id="4_1" index="1" speed="13.89" length="64.02" width="5.38" shape="217.75,131.76 217.12,119.30 216.58,115.63 215.68,112.03 214.43,108.54 212.85,105.19 210.93,102.01 208.73,99.03 206.24,96.27 203.48,93.78 200.50,91.57 197.32,89.67 193.97,88.08 190.48,86.83 186.87,85.93 183.21,85.38 179.51,85.20"/>
    </edge>
    <edge id="5" from="43" to="25" priority="-1" shape="220.00,192.01 220.00,162.01">
        <lane id="5_0" index="0" speed="13.89" length="38.48" width="5.38" shape="211.93,191.74 211.93,153.26"/>
        <lane id="5_1" index="1" speed="13.89" length="38.48" width="5.38" shape="217.31,191.74 217.31,153.26"/>
    </edge>
    <edge id="6" from="44" to="43" priority="-1" shape="179.50,232.51 183.47,232.31 187.40,231.73 191.26,230.76 195.00,229.43 198.59,227.73 202.00,225.68 205.19,223.32 208.14,220.65 210.81,217.70 213.17,214.51 215.22,211.10 216.92,207.51 218.26,203.76 219.22,199.91 219.80,195.98 220.00,192.01">
        <lane id="6_0" index="0" speed="13.89" length="55.13" width="5.38" shape="179.36,224.44 182.68,224.27 185.82,223.81 188.92,223.03 191.91,221.96 194.77,220.61 197.51,218.96 200.07,217.07 202.43,214.94 204.56,212.58 206.45,210.02 208.10,207.28 209.46,204.42 210.53,201.42 211.29,198.34 211.76,195.19 211.93,191.87"/>
        <lane id="6_1" index="1" speed="13.89" length="55.13" width="5.38" shape="179.64,229.81 183.21,229.63 186.87,229.09 190.48,228.18 193.97,226.94 197.32,225.36 200.50,223.44 203.48,221.24 206.24,218.75 208.73,215.99 210.93,213.01 212.85,209.83 214.43,206.48 215.68,202.98 216.58,199.39 217.12,195.72 217.30,192.15"/>
    </edge>
    <edge id="7" from="26" to="44" priority="-1" shape="149.50,232.51 179.50,232.51">
        <lane id="7_0" index="0" speed="13.89" length="38.48" width="5.38" shape="140.75,224.44 179.23,224.44"/>
        <lane id="7_1" index="1" speed="13.89" length="38.48" width="5.38" shape="140.75,229.82 179.23,229.82"/>
    </edge>
    <edge id="8" from="42" to="41" priority="-1" shape="170.50,142.51 166.53,142.70 162.60,143.29 158.74,144.25 155.00,145.59 151.41,147.29 148.00,149.33 144.81,151.70 141.86,154.37 139.19,157.32 136.82,160.51 134.78,163.92 133.08,167.51 131.74,171.25 130.78,175.11 130.19,179.04 130.00,183.01">
        <lane id="8_0" index="0" speed="13.89" length="55.38" width="5.38" shape="170.75,150.58 167.32,150.74 164.18,151.21 161.08,151.98 158.10,153.05 155.22,154.42 152.49,156.05 149.94,157.95 147.57,160.08 145.44,162.45 143.54,165.00 141.91,167.73 140.54,170.61 139.47,173.59 138.70,176.69 138.23,179.83 138.07,183.26"/>
        <lane id="8_1" index="1" speed="13.89" length="55.38" width="5.38" shape="170.49,145.20 166.79,145.38 163.13,145.93 159.52,146.83 156.03,148.08 152.68,149.67 149.50,151.57 146.52,153.78 143.76,156.27 141.27,159.03 139.06,162.01 137.16,165.19 135.57,168.54 134.32,172.03 133.42,175.64 132.87,179.30 132.69,183.00"/>
    </edge>
    <edge id="9" from="42" to="25" priority="-1" shape="170.50,142.51 200.50,142.51">
        <lane id="9_0" index="0" speed="13.89" length="38.62" width="5.38" shape="170.63,134.44 209.25,134.44"/>
        <lane id="9_1" index="1" speed="13.89" length="38.62" width="5.38" shape="170.63,139.82 209.25,139.82"/>
    </edge>
    <edge id="gneE10" from="gneJ18" to="gneJ21" priority="-1">
        <lane id="gneE10_0" index="0" speed="13.89" length="88.00" shape="0.00,-27.60 88.00,-27.60"/>
    </edge>
    <edge id="gneE11" from="gneJ22" to="gneJ23" priority="-1">
        <lane id="gneE11_0" index="0" speed="13.89" length="17.20" shape="53.48,-56.93 53.48,-39.73"/>
    </edge>
    <edge id="gneE12" from="gneJ24" to="gneJ25" priority="-1">
        <lane id="gneE12_0" index="0" speed="13.89" length="15.27" shape="123.16,-30.66 123.35,-45.93"/>
    </edge>
    <edge id="gneE13" from="gneJ26" to="gneJ27" priority="-1">
        <lane id="gneE13_0" index="0" speed="13.89" length="52.15" shape="348.60,97.75 348.77,149.90"/>
    </edge>
    <edge id="gneE16" from="gneJ32" to="gneJ33" priority="-1">
        <lane id="gneE16_0" index="0" speed="13.89" length="51.77" shape="342.82,97.96 342.72,149.73"/>
    </edge>
    <edge id="gneE17" from="gneJ35" to="gneJ36" priority="-1">
        <lane id="gneE17_0" index="0" speed="13.89" length="37.99" shape="342.70,186.07 343.01,224.06"/>
    </edge>
    <edge id="gneE21" from="gneJ43" to="gneJ44" priority="-1">
        <lane id="gneE21_0" index="0" speed="13.89" length="79.52" shape="8.50,-2.54 88.02,-2.73"/>
    </edge>
    <edge id="gneE22" from="gneJ45" to="gneJ46" priority="-1">
        <lane id="gneE22_0" index="0" speed="13.89" length="73.19" shape="210.07,8.90 136.88,8.90"/>
    </edge>
    <edge id="gneE23" from="gneJ47" to="gneJ48" priority="-1">
        <lane id="gneE23_0" index="0" speed="13.89" length="72.29" shape="209.53,3.10 137.24,3.10"/>
    </edge>
    <edge id="gneE26" from="gneJ53" to="gneJ54" priority="-1">
        <lane id="gneE26_0" index="0" speed="13.89" length="18.46" shape="358.18,206.91 339.72,207.00"/>
    </edge>
    <edge id="gneE28" from="gneJ57" to="gneJ58" priority="-1">
        <lane id="gneE28_0" index="0" speed="13.89" length="52.23" shape="224.75,418.58 172.52,418.55"/>
    </edge>
    <edge id="gneE29" from="gneJ59" to="gneJ60" priority="-1">
        <lane id="gneE29_0" index="0" speed="13.89" length="42.66" shape="151.87,424.39 109.21,424.44"/>
    </edge>
    <edge id="gneE3" from="gneJ6" to="gneJ7" priority="-1">
        <lane id="gneE3_0" index="0" speed="13.89" length="250.00" shape="0.00,-42.60 250.00,-42.60"/>
    </edge>
    <edge id="gneE30" from="gneJ61" to="gneJ62" priority="-1">
        <lane id="gneE30_0" index="0" speed="13.89" length="42.31" shape="152.06,418.33 109.75,418.27"/>
    </edge>
    <edge id="gneE31" from="gneJ63" to="gneJ64" priority="-1">
        <lane id="gneE31_0" index="0" speed="13.89" length="46.40" shape="90.96,419.05 44.56,418.86"/>
    </edge>
    <edge id="gneE32" from="gneJ65" to="gneJ66" priority="-1">
        <lane id="gneE32_0" index="0" speed="13.89" length="46.40" shape="90.97,413.28 44.57,413.19"/>
    </edge>
    <edge id="gneE8" from="gneJ16" to="gneJ17" priority="-1">
        <lane id="gneE8_0" index="0" speed="13.89" length="78.57" shape="87.62,7.90 9.05,7.82"/>
    </edge>
    <edge id="gneE9" from="gneJ18" to="gneJ19" priority="-1">
        <lane id="gneE9_0" index="0" speed="13.89" length="250.00" shape="0.00,-27.60 250.00,-27.60"/>
    </edge>

    <junction id="25" type="priority" x="220.00" y="142.51" incLanes="5_0 5_1 10_0 10_1 -4_0 -4_1 9_0 9_1" intLanes=":25_0_0 :25_1_0 :25_1_1 :25_3_0 :25_4_0 :25_5_0 :25_6_0 :25_6_1 :25_20_0 :25_21_0 :25_10_0 :25_11_0 :25_11_1 :25_13_0 :25_14_0 :25_15_0 :25_16_0 :25_16_1 :25_22_0 :25_23_0" shape="230.75,153.26 209.25,153.26 209.25,131.76 230.75,131.76" customShape="1">
        <request index="0"  response="00000000000011000000" foes="00000000000011000000" cont="0"/>
        <request index="1"  response="01111000000111000000" foes="01111110000111000000" cont="0"/>
        <request index="2"  response="01111000000111000000" foes="01111110000111000000" cont="0"/>
        <request index="3"  response="01110001100111000000" foes="01110001101111000000" cont="0"/>
        <request index="4"  response="01000001100000000000" foes="01000001100000000000" cont="0"/>
        <request index="5"  response="00000000000000000000" foes="00000001100000000000" cont="0"/>
        <request index="6"  response="00000000000000000000" foes="11000011100000001111" cont="0"/>
        <request index="7"  response="00000000000000000000" foes="11000011100000001111" cont="0"/>
        <request index="8"  response="00110000000000000000" foes="00110111100000001110" cont="1"/>
        <request index="9"  response="00110000000000001000" foes="00110000000000001000" cont="1"/>
        <request index="10" response="00110000000000000000" foes="00110000000000000000" cont="0"/>
        <request index="11" response="01110000000111100000" foes="01110000000111111000" cont="0"/>
        <request index="12" response="01110000000111100000" foes="01110000000111111000" cont="0"/>
        <request index="13" response="01110000000111000110" foes="11110000000111000110" cont="0"/>
        <request index="14" response="00000000000100000110" foes="00000000000100000110" cont="0"/>
        <request index="15" response="00000000000000000000" foes="00000000000000000110" cont="0"/>
        <request index="16" response="00000000000000000000" foes="00000011111100001110" cont="0"/>
        <request index="17" response="00000000000000000000" foes="00000011111100001110" cont="0"/>
        <request index="18" response="00000000000011000000" foes="00000011100011011110" cont="1"/>
        <request index="19" response="00000010000011000000" foes="00000010000011000000" cont="1"/>
    </junction>
    <junction id="26" type="priority" x="130.00" y="232.51" incLanes="-24_0 -24_1 -7_0 -7_1 -23_0 -23_1 0_0 0_1" intLanes=":26_0_0 :26_1_0 :26_1_1 :26_20_0 :26_21_0 :26_5_0 :26_6_0 :26_6_1 :26_8_0 :26_9_0 :26_10_0 :26_11_0 :26_11_1 :26_22_0 :26_23_0 :26_15_0 :26_16_0 :26_16_1 :26_18_0 :26_19_0" shape="140.75,243.26 119.25,243.26 119.25,221.76 140.75,221.76" customShape="1">
        <request index="0"  response="00000000000000000000" foes="00000000000011000000" cont="0"/>
        <request index="1"  response="00000000000000000000" foes="01111110000111000000" cont="0"/>
        <request index="2"  response="00000000000000000000" foes="01111110000111000000" cont="0"/>
        <request index="3"  response="00000001100000000000" foes="01110001101111000000" cont="1"/>
        <request index="4"  response="01000001100000000000" foes="01000001100000000000" cont="1"/>
        <request index="5"  response="00000001100000000000" foes="00000001100000000000" cont="0"/>
        <request index="6"  response="00000011100000001111" foes="11000011100000001111" cont="0"/>
        <request index="7"  response="00000011100000001111" foes="11000011100000001111" cont="0"/>
        <request index="8"  response="00110011100000001110" foes="00110111100000001110" cont="0"/>
        <request index="9"  response="00110000000000001000" foes="00110000000000001000" cont="0"/>
        <request index="10" response="00000000000000000000" foes="00110000000000000000" cont="0"/>
        <request index="11" response="00000000000000000000" foes="01110000000111111000" cont="0"/>
        <request index="12" response="00000000000000000000" foes="01110000000111111000" cont="0"/>
        <request index="13" response="00000000000000000110" foes="11110000000111000110" cont="1"/>
        <request index="14" response="00000000000100000110" foes="00000000000100000110" cont="1"/>
        <request index="15" response="00000000000000000110" foes="00000000000000000110" cont="0"/>
        <request index="16" response="00000011110000001110" foes="00000011111100001110" cont="0"/>
        <request index="17" response="00000011110000001110" foes="00000011111100001110" cont="0"/>
        <request index="18" response="00000011100011001110" foes="00000011100011011110" cont="0"/>
        <request index="19" response="00000010000011000000" foes="00000010000011000000" cont="0"/>
    </junction>
    <junction id="27" type="priority" x="280.00" y="280.51" incLanes="-16_0 -16_1 -20_0 -20_1 17_0 17_1 -12_0 -12_1" intLanes=":27_0_0 :27_1_0 :27_1_1 :27_20_0 :27_21_0 :27_5_0 :27_6_0 :27_6_1 :27_8_0 :27_9_0 :27_10_0 :27_11_0 :27_11_1 :27_22_0 :27_23_0 :27_15_0 :27_16_0 :27_16_1 :27_18_0 :27_19_0" shape="290.75,291.26 269.25,291.26 269.25,269.76 290.75,269.76" customShape="1">
        <request index="0"  response="00000000000000000000" foes="00000000000011000000" cont="0"/>
        <request index="1"  response="00000000000000000000" foes="01111110000111000000" cont="0"/>
        <request index="2"  response="00000000000000000000" foes="01111110000111000000" cont="0"/>
        <request index="3"  response="00000001100000000000" foes="01110001101111000000" cont="1"/>
        <request index="4"  response="01000001100000000000" foes="01000001100000000000" cont="1"/>
        <request index="5"  response="00000001100000000000" foes="00000001100000000000" cont="0"/>
        <request index="6"  response="00000011100000001111" foes="11000011100000001111" cont="0"/>
        <request index="7"  response="00000011100000001111" foes="11000011100000001111" cont="0"/>
        <request index="8"  response="00110011100000001110" foes="00110111100000001110" cont="0"/>
        <request index="9"  response="00110000000000001000" foes="00110000000000001000" cont="0"/>
        <request index="10" response="00000000000000000000" foes="00110000000000000000" cont="0"/>
        <request index="11" response="00000000000000000000" foes="01110000000111111000" cont="0"/>
        <request index="12" response="00000000000000000000" foes="01110000000111111000" cont="0"/>
        <request index="13" response="00000000000000000110" foes="11110000000111000110" cont="1"/>
        <request index="14" response="00000000000100000110" foes="00000000000100000110" cont="1"/>
        <request index="15" response="00000000000000000110" foes="00000000000000000110" cont="0"/>
        <request index="16" response="00000011110000001110" foes="00000011111100001110" cont="0"/>
        <request index="17" response="00000011110000001110" foes="00000011111100001110" cont="0"/>
        <request index="18" response="00000011100011001110" foes="00000011100011011110" cont="0"/>
        <request index="19" response="00000010000011000000" foes="00000010000011000000" cont="0"/>
    </junction>
    <junction id="28" type="priority" x="251.50" y="280.51" incLanes="12_0 12_1 -18_0 -18_1" intLanes=":28_0_0 :28_0_1 :28_2_0 :28_2_1" shape="251.63,291.27 251.63,269.75 250.85,269.77 251.88,291.26">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="29" type="priority" x="211.00" y="321.01" incLanes="18_0 18_1 21_0 21_1" intLanes=":29_0_0 :29_0_1 :29_2_0 :29_2_1" shape="221.75,321.55 200.25,320.47 221.75,321.55">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="30" type="priority" x="170.50" y="361.51" incLanes="-21_0 -21_1 22_0 22_1" intLanes=":30_0_0 :30_0_1 :30_2_0 :30_2_1" shape="171.58,372.23 170.50,350.74 169.42,372.23 170.22,372.27 170.50,372.27 170.78,372.27 171.12,372.25">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="31" type="priority" x="130.00" y="321.01" incLanes="-22_0 -22_1 24_0 24_1" intLanes=":31_0_0 :31_0_1 :31_2_0 :31_2_1" shape="119.27,321.82 140.76,320.74 119.24,320.74">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="32" type="priority" x="239.50" y="418.51" incLanes="-19_0 -19_1 -15_0" intLanes=":32_0_0 :32_0_1 :32_2_0 :32_2_1" shape="243.97,429.06 242.89,407.52 240.14,408.48 239.24,409.10 238.34,409.68 237.19,410.10 235.56,410.26 235.56,426.76 238.71,427.40 239.77,427.99 240.83,428.56 242.14,428.96">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="33" type="priority" x="280.00" y="378.01" incLanes="16_0 16_1 19_0 19_1" intLanes=":33_0_0 :33_0_1 :33_2_0 :33_2_1" shape="290.76,377.74 269.24,377.74 290.73,378.82">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="34" type="priority" x="340.00" y="240.01" incLanes="-14_0 -14_1 20_0 20_1" intLanes=":34_0_0 :34_0_1 :34_2_0 :34_2_1" shape="350.76,239.74 329.24,239.74 350.73,240.82">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="35" type="priority" x="340.00" y="90.01" incLanes="14_0 14_1 -11_0 -11_1" intLanes=":35_0_0 :35_0_1 :35_2_0 :35_2_1" shape="329.24,90.15 350.76,90.15 350.74,89.35 329.25,90.40">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="36" type="priority" x="250.00" y="0.01" incLanes="11_0 11_1 -13_0 -13_1" intLanes=":36_0_0 :36_0_1 :36_2_0 :36_2_1" shape="249.61,10.76 250.66,-10.73 249.86,-10.75 249.86,10.77">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="37" type="priority" x="179.50" y="82.51" incLanes="4_0 4_1 3_0 3_1" intLanes=":37_0_0 :37_0_1 :37_2_0 :37_2_1" shape="179.12,93.26 180.15,71.77 179.37,71.75 179.37,93.27">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="38" type="priority" x="110.50" y="82.51" incLanes="-3_0 -3_1 -2_0 -2_1" intLanes=":38_0_0 :38_0_1 :38_2_0 :38_2_1" shape="110.63,93.27 110.63,71.75 109.85,71.77 110.88,93.26">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="39" type="priority" x="70.00" y="123.01" incLanes="1_0 1_1 2_0 2_1" intLanes=":39_0_0 :39_0_1 :39_2_0 :39_2_1" shape="59.24,123.14 80.76,123.14 80.75,123.39 59.26,122.36">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="40" type="priority" x="70.00" y="192.01" incLanes="-0_0 -0_1 -1_0 -1_1" intLanes=":40_0_0 :40_0_1 :40_2_0 :40_2_1" shape="59.27,192.82 80.76,191.74 59.24,191.74">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="41" type="priority" x="130.00" y="183.01" incLanes="23_0 23_1 8_0 8_1" intLanes=":41_0_0 :41_0_1 :41_2_0 :41_2_1" shape="119.24,183.14 140.76,183.14 140.75,183.39 119.26,182.36">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="42" type="priority" x="170.50" y="142.51" incLanes="-9_0 -9_1 -8_0 -8_1" intLanes=":42_0_0 :42_0_1 :42_2_0 :42_2_1" shape="170.63,153.27 170.63,131.75 169.85,131.77 170.88,153.26">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="43" type="priority" x="220.00" y="192.01" incLanes="-5_0 -5_1 6_0 6_1" intLanes=":43_0_0 :43_0_1 :43_2_0 :43_2_1" shape="230.76,191.74 209.24,191.74 230.73,192.82">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="44" type="priority" x="179.50" y="232.51" incLanes="-6_0 -6_1 7_0 7_1" intLanes=":44_0_0 :44_0_1 :44_2_0 :44_2_1" shape="180.31,243.24 179.23,221.75 179.23,243.27">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="45" type="priority" x="280.00" y="183.01" incLanes="-17_0 -17_1 -10_0 -10_1" intLanes=":45_0_0 :45_0_1 :45_2_0 :45_2_1" shape="269.24,183.28 290.76,183.28 290.73,182.20">
        <request index="0" response="0000" foes="0000" cont="0"/>
        <request index="1" response="0000" foes="0000" cont="0"/>
        <request index="2" response="0000" foes="0000" cont="0"/>
        <request index="3" response="0000" foes="0000" cont="0"/>
    </junction>
    <junction id="Custom22" type="priority" x="0.00" y="0.00" incLanes="13_0 13_1" intLanes=":Custom22_0_0" shape="0.00,0.00 -0.00,10.76 0.00,0.00">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="Custom23" type="priority" x="39.50" y="418.51" incLanes="15_0 15_1" intLanes=":Custom23_0_0" shape="39.50,415.76 39.50,426.76 39.50,415.76">
        <request index="0" response="0" foes="0" cont="0"/>
    </junction>
    <junction id="gneJ16" type="dead_end" x="87.62" y="6.30" incLanes="" intLanes="" shape="87.62,6.30 87.62,9.50"/>
    <junction id="gneJ17" type="dead_end" x="9.05" y="6.22" incLanes="gneE8_0" intLanes="" shape="9.05,9.42 9.05,6.22"/>
    <junction id="gneJ18" type="dead_end" x="0.00" y="-26.00" incLanes="" intLanes="" shape="0.00,-26.00 0.00,-29.20 0.00,-26.00 0.00,-29.20"/>
    <junction id="gneJ19" type="dead_end" x="250.00" y="-26.00" incLanes="gneE9_0" intLanes="" shape="250.00,-29.20 250.00,-26.00"/>
    <junction id="gneJ21" type="dead_end" x="88.00" y="-26.00" incLanes="gneE10_0" intLanes="" shape="88.00,-29.20 88.00,-26.00"/>
    <junction id="gneJ22" type="dead_end" x="51.88" y="-56.93" incLanes="" intLanes="" shape="51.88,-56.93 55.08,-56.93"/>
    <junction id="gneJ23" type="dead_end" x="51.88" y="-39.73" incLanes="gneE11_0" intLanes="" shape="55.08,-39.73 51.88,-39.73"/>
    <junction id="gneJ24" type="dead_end" x="124.76" y="-30.64" incLanes="" intLanes="" shape="124.76,-30.64 121.56,-30.68"/>
    <junction id="gneJ25" type="dead_end" x="124.95" y="-45.91" incLanes="gneE12_0" intLanes="" shape="121.75,-45.95 124.95,-45.91"/>
    <junction id="gneJ26" type="dead_end" x="347.00" y="97.76" incLanes="" intLanes="" shape="347.00,97.76 350.20,97.75"/>
    <junction id="gneJ27" type="dead_end" x="347.17" y="149.91" incLanes="gneE13_0" intLanes="" shape="350.37,149.90 347.17,149.91"/>
    <junction id="gneJ32" type="dead_end" x="341.22" y="97.96" incLanes="" intLanes="" shape="341.22,97.96 344.42,97.97"/>
    <junction id="gneJ33" type="dead_end" x="341.12" y="149.73" incLanes="gneE16_0" intLanes="" shape="344.32,149.74 341.12,149.73"/>
    <junction id="gneJ35" type="dead_end" x="341.10" y="186.08" incLanes="" intLanes="" shape="341.10,186.08 344.30,186.05"/>
    <junction id="gneJ36" type="dead_end" x="341.41" y="224.07" incLanes="gneE17_0" intLanes="" shape="344.61,224.04 341.41,224.07"/>
    <junction id="gneJ43" type="dead_end" x="8.50" y="-0.94" incLanes="" intLanes="" shape="8.50,-0.94 8.49,-4.14"/>
    <junction id="gneJ44" type="dead_end" x="88.02" y="-1.13" incLanes="gneE21_0" intLanes="" shape="88.01,-4.33 88.02,-1.13"/>
    <junction id="gneJ45" type="dead_end" x="210.07" y="7.30" incLanes="" intLanes="" shape="210.07,7.30 210.07,10.50"/>
    <junction id="gneJ46" type="dead_end" x="136.88" y="7.30" incLanes="gneE22_0" intLanes="" shape="136.88,10.50 136.88,7.30"/>
    <junction id="gneJ47" type="dead_end" x="209.53" y="1.50" incLanes="" intLanes="" shape="209.53,1.50 209.53,4.70"/>
    <junction id="gneJ48" type="dead_end" x="137.24" y="1.50" incLanes="gneE23_0" intLanes="" shape="137.24,4.70 137.24,1.50"/>
    <junction id="gneJ53" type="dead_end" x="358.17" y="205.31" incLanes="" intLanes="" shape="358.17,205.31 358.19,208.51"/>
    <junction id="gneJ54" type="dead_end" x="339.71" y="205.40" incLanes="gneE26_0" intLanes="" shape="339.73,208.60 339.71,205.40"/>
    <junction id="gneJ57" type="dead_end" x="224.75" y="416.98" incLanes="" intLanes="" shape="224.75,416.98 224.75,420.18"/>
    <junction id="gneJ58" type="dead_end" x="172.52" y="416.95" incLanes="gneE28_0" intLanes="" shape="172.52,420.15 172.52,416.95"/>
    <junction id="gneJ59" type="dead_end" x="151.87" y="422.79" incLanes="" intLanes="" shape="151.87,422.79 151.87,425.99"/>
    <junction id="gneJ6" type="dead_end" x="0.00" y="-41.00" incLanes="" intLanes="" shape="0.00,-41.00 0.00,-44.20"/>
    <junction id="gneJ60" type="dead_end" x="109.21" y="422.84" incLanes="gneE29_0" intLanes="" shape="109.21,426.04 109.21,422.84"/>
    <junction id="gneJ61" type="dead_end" x="152.06" y="416.73" incLanes="" intLanes="" shape="152.06,416.73 152.06,419.93"/>
    <junction id="gneJ62" type="dead_end" x="109.75" y="416.67" incLanes="gneE30_0" intLanes="" shape="109.75,419.87 109.75,416.67"/>
    <junction id="gneJ63" type="dead_end" x="90.97" y="417.45" incLanes="" intLanes="" shape="90.97,417.45 90.96,420.65"/>
    <junction id="gneJ64" type="dead_end" x="44.57" y="417.26" incLanes="gneE31_0" intLanes="" shape="44.56,420.46 44.57,417.26"/>
    <junction id="gneJ65" type="dead_end" x="90.97" y="411.68" incLanes="" intLanes="" shape="90.97,411.68 90.96,414.88"/>
    <junction id="gneJ66" type="dead_end" x="44.57" y="411.59" incLanes="gneE32_0" intLanes="" shape="44.56,414.79 44.57,411.59"/>
    <junction id="gneJ7" type="dead_end" x="250.00" y="-41.00" incLanes="gneE3_0" intLanes="" shape="250.00,-44.20 250.00,-41.00"/>

    <junction id=":25_20_0" type="internal" x="222.91" y="142.24" incLanes=":25_8_0 9_0 9_1" intLanes=":25_1_0 :25_1_1 :25_3_0 :25_11_0 :25_11_1 :25_13_0 :25_14_0 :25_15_0 :25_16_0 :25_16_1"/>
    <junction id=":25_21_0" type="internal" x="228.06" y="142.09" incLanes=":25_9_0 -4_0 5_1 9_0 9_1" intLanes=":25_3_0 :25_10_0 :25_16_0 :25_16_1"/>
    <junction id=":25_22_0" type="internal" x="217.93" y="142.34" incLanes=":25_18_0 10_0 10_1" intLanes=":25_1_0 :25_1_1 :25_3_0 :25_4_0 :25_5_0 :25_6_0 :25_6_1 :25_11_0 :25_11_1 :25_13_0"/>
    <junction id=":25_23_0" type="internal" x="211.94" y="142.51" incLanes=":25_19_0 -4_1 10_0 10_1 5_0" intLanes=":25_0_0 :25_6_0 :25_6_1 :25_13_0"/>
    <junction id=":26_20_0" type="internal" x="131.23" y="234.75" incLanes=":26_3_0 -23_0 -23_1" intLanes=":26_6_0 :26_6_1 :26_8_0 :26_9_0 :26_10_0 :26_11_0 :26_11_1 :26_16_0 :26_16_1 :26_18_0"/>
    <junction id=":26_21_0" type="internal" x="130.00" y="240.57" incLanes=":26_4_0 -23_0 -23_1 -7_0 0_1" intLanes=":26_5_0 :26_11_0 :26_11_1 :26_18_0"/>
    <junction id=":26_22_0" type="internal" x="128.64" y="230.86" incLanes=":26_13_0 -24_0 -24_1" intLanes=":26_0_0 :26_1_0 :26_1_1 :26_6_0 :26_6_1 :26_8_0 :26_16_0 :26_16_1 :26_18_0 :26_19_0"/>
    <junction id=":26_23_0" type="internal" x="130.00" y="224.45" incLanes=":26_14_0 -24_0 -24_1 -7_1 0_0" intLanes=":26_1_0 :26_1_1 :26_8_0 :26_15_0"/>
    <junction id=":27_20_0" type="internal" x="281.10" y="283.34" incLanes=":27_3_0 17_0 17_1" intLanes=":27_6_0 :27_6_1 :27_8_0 :27_9_0 :27_10_0 :27_11_0 :27_11_1 :27_16_0 :27_16_1 :27_18_0"/>
    <junction id=":27_21_0" type="internal" x="280.00" y="288.57" incLanes=":27_4_0 -12_1 -20_0 17_0 17_1" intLanes=":27_5_0 :27_11_0 :27_11_1 :27_18_0"/>
    <junction id=":27_22_0" type="internal" x="278.77" y="278.27" incLanes=":27_13_0 -16_0 -16_1" intLanes=":27_0_0 :27_1_0 :27_1_1 :27_6_0 :27_6_1 :27_8_0 :27_16_0 :27_16_1 :27_18_0 :27_19_0"/>
    <junction id=":27_23_0" type="internal" x="280.00" y="272.45" incLanes=":27_14_0 -12_0 -16_0 -16_1 -20_1" intLanes=":27_1_0 :27_1_1 :27_8_0 :27_15_0"/>

    <connection from="-0" to="1" fromLane="0" toLane="0" via=":40_0_0" dir="s" state="M"/>
    <connection from="-0" to="1" fromLane="1" toLane="1" via=":40_0_1" dir="s" state="M"/>
    <connection from="-1" to="0" fromLane="0" toLane="0" via=":40_2_0" dir="s" state="M"/>
    <connection from="-1" to="0" fromLane="1" toLane="1" via=":40_2_1" dir="s" state="M"/>
    <connection from="-10" to="17" fromLane="0" toLane="0" via=":45_2_0" dir="s" state="M"/>
    <connection from="-10" to="17" fromLane="1" toLane="1" via=":45_2_1" dir="s" state="M"/>
    <connection from="-11" to="-14" fromLane="0" toLane="0" via=":35_2_0" dir="s" state="M"/>
    <connection from="-11" to="-14" fromLane="1" toLane="1" via=":35_2_1" dir="s" state="M"/>
    <connection from="-12" to="-17" fromLane="0" toLane="0" via=":27_15_0" dir="r" state="m"/>
    <connection from="-12" to="20" fromLane="0" toLane="0" via=":27_16_0" dir="s" state="m"/>
    <connection from="-12" to="20" fromLane="1" toLane="1" via=":27_16_1" dir="s" state="m"/>
    <connection from="-12" to="16" fromLane="1" toLane="1" via=":27_18_0" dir="l" state="m"/>
    <connection from="-12" to="12" fromLane="1" toLane="1" via=":27_19_0" dir="t" state="m"/>
    <connection from="-13" to="-11" fromLane="0" toLane="0" via=":36_2_0" dir="s" state="M"/>
    <connection from="-13" to="-11" fromLane="1" toLane="1" via=":36_2_1" dir="s" state="M"/>
    <connection from="-14" to="-20" fromLane="0" toLane="0" via=":34_0_0" dir="s" state="M"/>
    <connection from="-14" to="-20" fromLane="1" toLane="1" via=":34_0_1" dir="s" state="M"/>
    <connection from="-15" to="19" fromLane="0" toLane="0" via=":32_2_0" dir="s" state="M"/>
    <connection from="-15" to="19" fromLane="0" toLane="1" via=":32_2_1" dir="s" state="M"/>
    <connection from="-16" to="12" fromLane="0" toLane="0" via=":27_0_0" dir="r" state="M"/>
    <connection from="-16" to="-17" fromLane="0" toLane="0" via=":27_1_0" dir="s" state="M"/>
    <connection from="-16" to="-17" fromLane="1" toLane="1" via=":27_1_1" dir="s" state="M"/>
    <connection from="-16" to="20" fromLane="1" toLane="1" via=":27_3_0" dir="l" state="m"/>
    <connection from="-16" to="16" fromLane="1" toLane="1" via=":27_4_0" dir="t" state="m"/>
    <connection from="-17" to="10" fromLane="0" toLane="0" via=":45_0_0" dir="s" state="M"/>
    <connection from="-17" to="10" fromLane="1" toLane="1" via=":45_0_1" dir="s" state="M"/>
    <connection from="-18" to="-12" fromLane="0" toLane="0" via=":28_2_0" dir="s" state="M"/>
    <connection from="-18" to="-12" fromLane="1" toLane="1" via=":28_2_1" dir="s" state="M"/>
    <connection from="-19" to="15" fromLane="0" toLane="0" via=":32_0_0" dir="s" state="M"/>
    <connection from="-19" to="15" fromLane="1" toLane="1" via=":32_0_1" dir="s" state="M"/>
    <connection from="-2" to="3" fromLane="0" toLane="0" via=":38_2_0" dir="s" state="M"/>
    <connection from="-2" to="3" fromLane="1" toLane="1" via=":38_2_1" dir="s" state="M"/>
    <connection from="-20" to="16" fromLane="0" toLane="0" via=":27_5_0" dir="r" state="m"/>
    <connection from="-20" to="12" fromLane="0" toLane="0" via=":27_6_0" dir="s" state="m"/>
    <connection from="-20" to="12" fromLane="1" toLane="1" via=":27_6_1" dir="s" state="m"/>
    <connection from="-20" to="-17" fromLane="1" toLane="1" via=":27_8_0" dir="l" state="m"/>
    <connection from="-20" to="20" fromLane="1" toLane="1" via=":27_9_0" dir="t" state="m"/>
    <connection from="-21" to="-22" fromLane="0" toLane="0" via=":30_0_0" dir="s" state="M"/>
    <connection from="-21" to="-22" fromLane="1" toLane="1" via=":30_0_1" dir="s" state="M"/>
    <connection from="-22" to="-24" fromLane="0" toLane="0" via=":31_0_0" dir="s" state="M"/>
    <connection from="-22" to="-24" fromLane="1" toLane="1" via=":31_0_1" dir="s" state="M"/>
    <connection from="-23" to="7" fromLane="0" toLane="0" via=":26_10_0" dir="r" state="M"/>
    <connection from="-23" to="24" fromLane="0" toLane="0" via=":26_11_0" dir="s" state="M"/>
    <connection from="-23" to="24" fromLane="1" toLane="1" via=":26_11_1" dir="s" state="M"/>
    <connection from="-23" to="-0" fromLane="1" toLane="1" via=":26_13_0" dir="l" state="m"/>
    <connection from="-23" to="23" fromLane="1" toLane="1" via=":26_14_0" dir="t" state="m"/>
    <connection from="-24" to="-0" fromLane="0" toLane="0" via=":26_0_0" dir="r" state="M"/>
    <connection from="-24" to="23" fromLane="0" toLane="0" via=":26_1_0" dir="s" state="M"/>
    <connection from="-24" to="23" fromLane="1" toLane="1" via=":26_1_1" dir="s" state="M"/>
    <connection from="-24" to="7" fromLane="1" toLane="1" via=":26_3_0" dir="l" state="m"/>
    <connection from="-24" to="24" fromLane="1" toLane="1" via=":26_4_0" dir="t" state="m"/>
    <connection from="-3" to="2" fromLane="0" toLane="0" via=":38_0_0" dir="s" state="M"/>
    <connection from="-3" to="2" fromLane="1" toLane="1" via=":38_0_1" dir="s" state="M"/>
    <connection from="-4" to="-10" fromLane="0" toLane="0" via=":25_10_0" dir="r" state="m"/>
    <connection from="-4" to="-5" fromLane="0" toLane="0" via=":25_11_0" dir="s" state="m"/>
    <connection from="-4" to="-5" fromLane="1" toLane="1" via=":25_11_1" dir="s" state="m"/>
    <connection from="-4" to="-9" fromLane="1" toLane="1" via=":25_13_0" dir="l" state="m"/>
    <connection from="-4" to="4" fromLane="1" toLane="1" via=":25_14_0" dir="t" state="m"/>
    <connection from="-5" to="-6" fromLane="0" toLane="0" via=":43_0_0" dir="s" state="M"/>
    <connection from="-5" to="-6" fromLane="1" toLane="1" via=":43_0_1" dir="s" state="M"/>
    <connection from="-6" to="-7" fromLane="0" toLane="0" via=":44_0_0" dir="s" state="M"/>
    <connection from="-6" to="-7" fromLane="1" toLane="1" via=":44_0_1" dir="s" state="M"/>
    <connection from="-7" to="24" fromLane="0" toLane="0" via=":26_5_0" dir="r" state="m"/>
    <connection from="-7" to="-0" fromLane="0" toLane="0" via=":26_6_0" dir="s" state="m"/>
    <connection from="-7" to="-0" fromLane="1" toLane="1" via=":26_6_1" dir="s" state="m"/>
    <connection from="-7" to="23" fromLane="1" toLane="1" via=":26_8_0" dir="l" state="m"/>
    <connection from="-7" to="7" fromLane="1" toLane="1" via=":26_9_0" dir="t" state="m"/>
    <connection from="-8" to="9" fromLane="0" toLane="0" via=":42_2_0" dir="s" state="M"/>
    <connection from="-8" to="9" fromLane="1" toLane="1" via=":42_2_1" dir="s" state="M"/>
    <connection from="-9" to="8" fromLane="0" toLane="0" via=":42_0_0" dir="s" state="M"/>
    <connection from="-9" to="8" fromLane="1" toLane="1" via=":42_0_1" dir="s" state="M"/>
    <connection from="0" to="23" fromLane="0" toLane="0" via=":26_15_0" dir="r" state="m"/>
    <connection from="0" to="7" fromLane="0" toLane="0" via=":26_16_0" dir="s" state="m"/>
    <connection from="0" to="7" fromLane="1" toLane="1" via=":26_16_1" dir="s" state="m"/>
    <connection from="0" to="24" fromLane="1" toLane="1" via=":26_18_0" dir="l" state="m"/>
    <connection from="0" to="-0" fromLane="1" toLane="1" via=":26_19_0" dir="t" state="m"/>
    <connection from="1" to="-2" fromLane="0" toLane="0" via=":39_0_0" dir="s" state="M"/>
    <connection from="1" to="-2" fromLane="1" toLane="1" via=":39_0_1" dir="s" state="M"/>
    <connection from="10" to="-5" fromLane="0" toLane="0" via=":25_5_0" dir="r" state="M"/>
    <connection from="10" to="-9" fromLane="0" toLane="0" via=":25_6_0" dir="s" state="M"/>
    <connection from="10" to="-9" fromLane="1" toLane="1" via=":25_6_1" dir="s" state="M"/>
    <connection from="10" to="4" fromLane="1" toLane="1" via=":25_8_0" dir="l" state="m"/>
    <connection from="10" to="-10" fromLane="1" toLane="1" via=":25_9_0" dir="t" state="m"/>
    <connection from="11" to="13" fromLane="0" toLane="0" via=":36_0_0" dir="s" state="M"/>
    <connection from="11" to="13" fromLane="1" toLane="1" via=":36_0_1" dir="s" state="M"/>
    <connection from="12" to="18" fromLane="0" toLane="0" via=":28_0_0" dir="s" state="M"/>
    <connection from="12" to="18" fromLane="1" toLane="1" via=":28_0_1" dir="s" state="M"/>
    <connection from="13" to="-13" fromLane="1" toLane="1" via=":Custom22_0_0" dir="t" state="M"/>
    <connection from="14" to="11" fromLane="0" toLane="0" via=":35_0_0" dir="s" state="M"/>
    <connection from="14" to="11" fromLane="1" toLane="1" via=":35_0_1" dir="s" state="M"/>
    <connection from="15" to="-15" fromLane="1" toLane="0" via=":Custom23_0_0" dir="t" state="M"/>
    <connection from="16" to="-19" fromLane="0" toLane="0" via=":33_0_0" dir="s" state="M"/>
    <connection from="16" to="-19" fromLane="1" toLane="1" via=":33_0_1" dir="s" state="M"/>
    <connection from="17" to="20" fromLane="0" toLane="0" via=":27_10_0" dir="r" state="M"/>
    <connection from="17" to="16" fromLane="0" toLane="0" via=":27_11_0" dir="s" state="M"/>
    <connection from="17" to="16" fromLane="1" toLane="1" via=":27_11_1" dir="s" state="M"/>
    <connection from="17" to="12" fromLane="1" toLane="1" via=":27_13_0" dir="l" state="m"/>
    <connection from="17" to="-17" fromLane="1" toLane="1" via=":27_14_0" dir="t" state="m"/>
    <connection from="18" to="-21" fromLane="0" toLane="0" via=":29_0_0" dir="s" state="M"/>
    <connection from="18" to="-21" fromLane="1" toLane="1" via=":29_0_1" dir="s" state="M"/>
    <connection from="19" to="-16" fromLane="0" toLane="0" via=":33_2_0" dir="s" state="M"/>
    <connection from="19" to="-16" fromLane="1" toLane="1" via=":33_2_1" dir="s" state="M"/>
    <connection from="2" to="-1" fromLane="0" toLane="0" via=":39_2_0" dir="s" state="M"/>
    <connection from="2" to="-1" fromLane="1" toLane="1" via=":39_2_1" dir="s" state="M"/>
    <connection from="20" to="14" fromLane="0" toLane="0" via=":34_2_0" dir="s" state="M"/>
    <connection from="20" to="14" fromLane="1" toLane="1" via=":34_2_1" dir="s" state="M"/>
    <connection from="21" to="-18" fromLane="0" toLane="0" via=":29_2_0" dir="s" state="M"/>
    <connection from="21" to="-18" fromLane="1" toLane="1" via=":29_2_1" dir="s" state="M"/>
    <connection from="22" to="21" fromLane="0" toLane="0" via=":30_2_0" dir="s" state="M"/>
    <connection from="22" to="21" fromLane="1" toLane="1" via=":30_2_1" dir="s" state="M"/>
    <connection from="23" to="-8" fromLane="0" toLane="0" via=":41_0_0" dir="s" state="M"/>
    <connection from="23" to="-8" fromLane="1" toLane="1" via=":41_0_1" dir="s" state="M"/>
    <connection from="24" to="22" fromLane="0" toLane="0" via=":31_2_0" dir="s" state="M"/>
    <connection from="24" to="22" fromLane="1" toLane="1" via=":31_2_1" dir="s" state="M"/>
    <connection from="3" to="-4" fromLane="0" toLane="0" via=":37_2_0" dir="s" state="M"/>
    <connection from="3" to="-4" fromLane="1" toLane="1" via=":37_2_1" dir="s" state="M"/>
    <connection from="4" to="-3" fromLane="0" toLane="0" via=":37_0_0" dir="s" state="M"/>
    <connection from="4" to="-3" fromLane="1" toLane="1" via=":37_0_1" dir="s" state="M"/>
    <connection from="5" to="-9" fromLane="0" toLane="0" via=":25_0_0" dir="r" state="m"/>
    <connection from="5" to="4" fromLane="0" toLane="0" via=":25_1_0" dir="s" state="m"/>
    <connection from="5" to="4" fromLane="1" toLane="1" via=":25_1_1" dir="s" state="m"/>
    <connection from="5" to="-10" fromLane="1" toLane="1" via=":25_3_0" dir="l" state="m"/>
    <connection from="5" to="-5" fromLane="1" toLane="1" via=":25_4_0" dir="t" state="m"/>
    <connection from="6" to="5" fromLane="0" toLane="0" via=":43_2_0" dir="s" state="M"/>
    <connection from="6" to="5" fromLane="1" toLane="1" via=":43_2_1" dir="s" state="M"/>
    <connection from="7" to="6" fromLane="0" toLane="0" via=":44_2_0" dir="s" state="M"/>
    <connection from="7" to="6" fromLane="1" toLane="1" via=":44_2_1" dir="s" state="M"/>
    <connection from="8" to="-23" fromLane="0" toLane="0" via=":41_2_0" dir="s" state="M"/>
    <connection from="8" to="-23" fromLane="1" toLane="1" via=":41_2_1" dir="s" state="M"/>
    <connection from="9" to="4" fromLane="0" toLane="0" via=":25_15_0" dir="r" state="M"/>
    <connection from="9" to="-10" fromLane="0" toLane="0" via=":25_16_0" dir="s" state="M"/>
    <connection from="9" to="-10" fromLane="1" toLane="1" via=":25_16_1" dir="s" state="M"/>
    <connection from="9" to="-5" fromLane="1" toLane="1" via=":25_18_0" dir="l" state="m"/>
    <connection from="9" to="-9" fromLane="1" toLane="1" via=":25_19_0" dir="t" state="m"/>

    <connection from=":25_0" to="-9" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":25_1" to="4" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":25_1" to="4" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":25_3" to="-10" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":25_4" to="-5" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":25_5" to="-5" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":25_6" to="-9" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":25_6" to="-9" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":25_8" to="4" fromLane="0" toLane="1" via=":25_20_0" dir="l" state="m"/>
    <connection from=":25_20" to="4" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":25_9" to="-10" fromLane="0" toLane="1" via=":25_21_0" dir="t" state="m"/>
    <connection from=":25_21" to="-10" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":25_10" to="-10" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":25_11" to="-5" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":25_11" to="-5" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":25_13" to="-9" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":25_14" to="4" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":25_15" to="4" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":25_16" to="-10" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":25_16" to="-10" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":25_18" to="-5" fromLane="0" toLane="1" via=":25_22_0" dir="l" state="m"/>
    <connection from=":25_22" to="-5" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":25_19" to="-9" fromLane="0" toLane="1" via=":25_23_0" dir="t" state="m"/>
    <connection from=":25_23" to="-9" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":26_0" to="-0" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":26_1" to="23" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":26_1" to="23" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":26_3" to="7" fromLane="0" toLane="1" via=":26_20_0" dir="l" state="m"/>
    <connection from=":26_20" to="7" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":26_4" to="24" fromLane="0" toLane="1" via=":26_21_0" dir="t" state="m"/>
    <connection from=":26_21" to="24" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":26_5" to="24" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":26_6" to="-0" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":26_6" to="-0" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":26_8" to="23" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":26_9" to="7" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":26_10" to="7" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":26_11" to="24" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":26_11" to="24" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":26_13" to="-0" fromLane="0" toLane="1" via=":26_22_0" dir="l" state="m"/>
    <connection from=":26_22" to="-0" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":26_14" to="23" fromLane="0" toLane="1" via=":26_23_0" dir="t" state="m"/>
    <connection from=":26_23" to="23" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":26_15" to="23" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":26_16" to="7" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":26_16" to="7" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":26_18" to="24" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":26_19" to="-0" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":27_0" to="12" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":27_1" to="-17" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":27_1" to="-17" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":27_3" to="20" fromLane="0" toLane="1" via=":27_20_0" dir="l" state="m"/>
    <connection from=":27_20" to="20" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":27_4" to="16" fromLane="0" toLane="1" via=":27_21_0" dir="t" state="m"/>
    <connection from=":27_21" to="16" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":27_5" to="16" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":27_6" to="12" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":27_6" to="12" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":27_8" to="-17" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":27_9" to="20" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":27_10" to="20" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":27_11" to="16" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":27_11" to="16" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":27_13" to="12" fromLane="0" toLane="1" via=":27_22_0" dir="l" state="m"/>
    <connection from=":27_22" to="12" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":27_14" to="-17" fromLane="0" toLane="1" via=":27_23_0" dir="t" state="m"/>
    <connection from=":27_23" to="-17" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":27_15" to="-17" fromLane="0" toLane="0" dir="r" state="M"/>
    <connection from=":27_16" to="20" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":27_16" to="20" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":27_18" to="16" fromLane="0" toLane="1" dir="l" state="M"/>
    <connection from=":27_19" to="12" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":28_0" to="18" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":28_0" to="18" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":28_2" to="-12" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":28_2" to="-12" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":29_0" to="-21" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":29_0" to="-21" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":29_2" to="-18" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":29_2" to="-18" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":30_0" to="-22" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":30_0" to="-22" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":30_2" to="21" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":30_2" to="21" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":31_0" to="-24" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":31_0" to="-24" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":31_2" to="22" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":31_2" to="22" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":32_0" to="15" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":32_0" to="15" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":32_2" to="19" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":32_2" to="19" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":33_0" to="-19" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":33_0" to="-19" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":33_2" to="-16" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":33_2" to="-16" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":34_0" to="-20" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":34_0" to="-20" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":34_2" to="14" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":34_2" to="14" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":35_0" to="11" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":35_0" to="11" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":35_2" to="-14" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":35_2" to="-14" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":36_0" to="13" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":36_0" to="13" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":36_2" to="-11" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":36_2" to="-11" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":37_0" to="-3" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":37_0" to="-3" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":37_2" to="-4" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":37_2" to="-4" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":38_0" to="2" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":38_0" to="2" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":38_2" to="3" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":38_2" to="3" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":39_0" to="-2" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":39_0" to="-2" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":39_2" to="-1" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":39_2" to="-1" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":40_0" to="1" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":40_0" to="1" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":40_2" to="0" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":40_2" to="0" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":41_0" to="-8" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":41_0" to="-8" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":41_2" to="-23" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":41_2" to="-23" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":42_0" to="8" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":42_0" to="8" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":42_2" to="9" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":42_2" to="9" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":43_0" to="-6" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":43_0" to="-6" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":43_2" to="5" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":43_2" to="5" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":44_0" to="-7" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":44_0" to="-7" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":44_2" to="6" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":44_2" to="6" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":45_0" to="10" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":45_0" to="10" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":45_2" to="17" fromLane="0" toLane="0" dir="s" state="M"/>
    <connection from=":45_2" to="17" fromLane="1" toLane="1" dir="s" state="M"/>
    <connection from=":Custom22_0" to="-13" fromLane="0" toLane="1" dir="t" state="M"/>
    <connection from=":Custom23_0" to="-15" fromLane="0" toLane="0" dir="t" state="M"/>

</net>
