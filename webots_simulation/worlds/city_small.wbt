#VRML_SIM R2023a utf8


EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadIntersection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/devices/sick/protos/SickLms291.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/GenericTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CrossRoadsTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BuildingUnderConstruction.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Auditorium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/FastFoodRestaurant.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Oak.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Pine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Cypress.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Sassafras.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/BigSassafras.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/PedestrianCrossing.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/YieldSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwayPole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwaySign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/obstacles/protos/OilBarrel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Crossroad.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialTower.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Plastic.proto"
EXTERNPROTO "../protos/VelodyneVLP-16.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Windmill.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
WorldInfo {
  info [
    "Autonomous Vehicle Simulation"
"The simple controller example uses an on-board camera to follow the yellow road lines and a SICK sensor to avoid the obstacles."
"The control of the vehicle is done using the driver library."
"The vehicle based on the Car PROTO is modelled with realistic physics properties: motor torques, body mass, friction forces, suspensions, etc."
  ]
  title "City"
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8 
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.005291936304283044 -0.9998502983438514 -0.016473503326948042 4.689289339017536
  position 25.449388432965065 40.7141214078759 211.06417952942627
  near 1
  follow "chitu"
  followSmoothness 0
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.38 0.35 0.32
  visibilityRange 1000
}
DEF GROUND Solid {
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  boundingObject USE GROUND_PLANE
  locked TRUE
}
Windmill {
  translation 126.05 0 0
}
CurvedRoadSegment {
  translation -64.5 4.5 0.02
  rotation 0 0 1 1.5708
  id "0"
  startJunction "25"
  endJunction "17"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -105 4.5 0.02
  rotation 0 0 1 -1.5723853071795864
  name "road(1)"
  id "1"
  startJunction "25"
  endJunction "24"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation -64.5 -64.5 0.02
  rotation 0 0 1 3.14156
  name "road(2)"
  id "2"
  startJunction "23"
  endJunction "24"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -64.5 -105 0.02
  name "road(3)"
  id "3"
  startJunction "23"
  endJunction "22"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation 4.5 -64.5 0.02
  rotation 0 0 1 -1.5723853071795864
  name "road(4)"
  id "4"
  startJunction "16"
  endJunction "22"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
RoadIntersection {
  translation 45 -45 0.02
  rotation 0 0 1 0.785398
  id "16"
  connectedRoadIDs [
    "11"
"4"
"10"
"5"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75 
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
StraightRoadSegment {
  translation 45 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "28"
  endJunction "16"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 4.5 4.5 0.02
  name "road(6)"
  id "6"
  startJunction "29"
  endJunction "28"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -25.5 45 0.02
  name "road(7)"
  id "7"
  startJunction "17"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
RoadIntersection {
  translation -45 45 0.02
  rotation 0 0 1 0.785398
  name "road intersection(1)"
  id "17"
  connectedRoadIDs [
    "7"
"8"
"0"
"15"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75 
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
"https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
StraightRoadSegment {
  translation -45 25.5 0.02
  rotation 0 0 -1 1.5708
  name "road(8)"
  id "8"
  startJunction "17"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation -4.5 -4.5 0.02
  rotation 0 0 1 3.14156
  name "road(9)"
  id "9"
  startJunction "27"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -4.5 -45 0.02
  name "road(10)"
  id "10"
  startJunction "27"
  endJunction "16"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 64.5 -4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(11)"
  id "11"
  startJunction "21"
  endJunction "16"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation 105 -4.5 0.02
  rotation 0 0 1 1.57079
  name "road(12)"
  id "12"
  startJunction "21"
  endJunction "20"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation 64.5 64.5 0.02
  name "road(13)"
  id "13"
  startJunction "19"
  endJunction "20"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation 64.5 105 0.02
  rotation 0 0 1 3.14156
  name "road(14)"
  id "14"
  startJunction "19"
  endJunction "18"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation -4.5 64.5 0.02
  rotation 0 0 1 1.5708
  name "road(15)"
  id "15"
  startJunction "17"
  endJunction "18"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
DEF CAR1 BmwX5 {
  translation -13.23974263319257 36.693866565028834 0.3169215004261582
  rotation 0.0022469665652513474 0.0004679081682474549 0.9999973660981316 -2.730981959116938
  controller "<none>"
  sensorsSlotFront [
    SickLms291 {
      translation 0.06 0 0
    }
  ]
  sensorsSlotTop [
    Camera {
      translation 0.72 0 -0.05
      children [
        Transform {
          translation -0.05 0 0
          rotation 0 -1 0 1.5708
          children [
            Shape {
              appearance PBRAppearance {
                baseColor 0.8 0.8 0.8
                roughness 1
                metalness 0
              }
              geometry Cylinder {
                height 0.1
                radius 0.04
              }
            }
          ]
        }
      ]
      fieldOfView 1
      width 128
    }
  ]
  sensorsSlotCenter [
    GPS {
    }
    Gyro {
    }
    Display {
      width 200
      height 150
    }
  ]
}
GenericTrafficLight {
  translation -34.665 55.2732 0
  startGreen FALSE
  greenTime 10
  redTime 10
  state "red"
}
CrossRoadsTrafficLight {
  translation 45 -45 0
}
DEF STONES Solid {
  translation 5.03891 -136.158 -4.23581
  children [
    DEF STONES_GROUP Group {
      children [
        Pose {
          translation 0 2 -0.6
          children [
            Shape {
              appearance DEF OBJECTS_APPEARANCE PBRAppearance {
                baseColor 0.5 0.5 0.5
                roughness 1
                metalness 0
              }
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0.5 -3.5 -0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 4 2 -0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 6 -1 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 9 0 0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 5.5 -5 0.2
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0 0 0.05
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 10 5 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 1 6 0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13 -4 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13.5 1.5 0.4
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
      ]
    }
  ]
  name "solid(1)"
  boundingObject USE STONES_GROUP
}
BuildingUnderConstruction {
  translation 112.102 110.896 0
}
BuildingUnderConstruction {
  translation 66.912 149.366 0
  rotation 0 0 1 -1.047195307179586
  name "building under construction(1)"
}
CommercialBuilding {
  translation 70.9574 31.6315 0
}
CyberboticsTower {
  translation -41.01 143.63 0
  name "Cyberbotics tower(1)"
}
UBuilding {
  translation -87.1466 81.9927 0
}
UBuilding {
  translation -87.1466 -139.307 0
  rotation 0 0 1 -1.8325953071795862
  name "U building(1)"
}
HollowBuilding {
}
HollowBuilding {
  translation 148.81 0 0
  name "hollow building(1)"
}
Hotel {
  translation -9.97953 71.6228 0
}
TheThreeTowers {
  translation 68.118 -90.636 0
}
TheThreeTowers {
  translation -152.374 -2.47097 0
  rotation 0 0 1 0.785398
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation 68.6779 -9.29537 0
  rotation 0 0 1 1.5708
}
BigGlassTower {
  translation -133.202 -74.5661 0
  rotation 0 0 1 1.5708
  name "big glass tower(1)"
}
Auditorium {
  translation -63.9296 -61.9719 0
  rotation 0 0 1 -0.9163
}
Museum {
  translation -0.191182 -68.6571 0
  rotation 0 0 1 1.5708
}
Museum {
  translation -32.4446 -141.8 0
  rotation 0 0 -1 -1.3090053071795866
  name "museum(1)"
}
ResidentialBuilding {
  translation -69.274 -1.81329 0
}
ResidentialBuilding {
  translation 150.106 64.8167 0
  name "residential building(1)"
}
FastFoodRestaurant {
  translation 51.749488 50.065561 0.004
  rotation 0 0 -1 1.8325926
  height 4.3
  length 11
  width 11
  brand "subway"
  numberOfSides 2
}
Oak {
  translation 81.7751 -19.8126 0
}
Pine {
  translation 50.4097 -99.1307 0
  name "tree(1)"
}
Cypress {
  translation 56.567 -81.7163 0
  name "tree(2)"
}
Sassafras {
  translation -36.8744 -75.9885 0
  name "tree(3)"
}
BigSassafras {
  translation -35.4355 -54.5924 0
  name "tree(4)"
}
Oak {
  translation 61.566 5.24018 0
  name "tree(5)"
}
Pine {
  translation -26.6541 -68.7408 0
  name "tree(6)"
}
Cypress {
  translation 26.6454 -62.6042 0
  name "tree(7)"
}
Sassafras {
  translation 68.1255 79.3778 0
  name "tree(8)"
}
BigSassafras {
  translation 121.7561 79.9763 0
  name "tree(9)"
}
Oak {
  translation 45.3761 87.5263 0
  name "tree(10)"
}
Pine {
  translation 4.80322 63.11 0
  name "tree(11)"
}
Cypress {
  translation -85.4708 4.92425 0
  name "tree(12)"
}
Sassafras {
  translation -85.8355 -9.61883 0
  name "tree(13)"
}
BigSassafras {
  translation -84.6727 -24.9703 0
  name "tree(14)"
}
Oak {
  translation -76.3128 81.8021 0
  name "tree(15)"
}
Pine {
  translation 81.4509 120.797 0
  name "tree(16)"
}
Cypress {
  translation -106.804 42.6205 0
  name "tree(17)"
}
Sassafras {
  translation -32.2671 115.416 0
  name "tree(18)"
}
BigSassafras {
  translation 121.921 33.1395 0
  name "tree(19)"
}
PedestrianCrossing {
  translation 45 -26 -0.06
  rotation 0 0 1 -3.1415853071795863
}
PedestrianCrossing {
  translation -61.4608 45.0693 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(1)"
}
CautionSign {
  translation -91.9275 48.9391 0
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_left.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(1)"
}
CautionSign {
  translation 33.842 10.5534 0
  rotation 0 0 1 1.7017
  name "caution sign(2)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/bump.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 84.01191 -26.81263 0
  rotation 0 0 1 0.6545
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
CautionSign {
  translation -5.43669 -34.1146 0
  rotation 0 0 1 -0.5236
  name "caution sign(4)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_right.jpg"
      ]
    }
  ]
}
OrderSign {
  translation -45.1363 34.6769 0
  rotation 0 0 1 1.5708
}
OrderSign {
  translation -55.4085 34.1742 0
  rotation 0 0 1 1.5708
  name "order sign(1)"
}
OrderSign {
  translation -67.6589 34.4983 0
  rotation 0 0 1 3.14159
  name "order sign(2)"
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_right_turn.jpg"
      ]
    }
  ]
}
OrderSign {
  translation 5.21302 94.5041 0
  rotation 0 0 1 3.14159
  name "order sign(3)"
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_pedestrian_crossing.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
  signBoards [
    StopPanel {
      translation 0 0 -0.097
    }
    OrderPanel {
      translation -0.03 0 -0.11
      rotation 0 0 1 3.1415926
    }
  ]
}
YieldSign {
  translation -55.468 66.4958 0
  rotation 0 0 1 1.5708
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 26.49986 -84.257796 0
  rotation 0 0 1 0.6545
  name "speed limit(1)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.317445 79.098744 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 87.1538 -50.335 0
  rotation 0 0 1 -3.14159
  name "speed limit(3)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 31.0289 -34.4459 0
  name "speed limit(4)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/one_way_sign_left.jpg"
      ]
    }
  ]
}
TrafficCone {
  translation -50.81735515582822 33.90679997826365 -0.004076718672611196
  rotation -0.0002731645447751274 0.9999998489904718 0.00047686493376705697 0.013690792761860552
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -53.34475515582822 33.90679997826365 -0.004076718672611196
  rotation -0.0002731645447751274 0.9999998489904718 0.00047686493376705697 0.013690792761860552
  name "traffic cone(1)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -48.16815515582822 34.009499978263655 -0.004076718672611196
  rotation -0.00027316454477513625 0.9999998489904718 0.0004768649337670819 0.013690792761860552
  name "traffic cone(2)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -45.09772295713029 26.34439846614276 -0.004076718672611196
  rotation -0.0005914901275885937 0.9988936837668098 0.04702189564662206 0.013705952159258806
  name "traffic cone(3)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -45.56035444800271 33.90824520601126 -0.004076718672611196
  rotation -0.00021751273136972268 0.9999707018454147 -0.007651675568151922 0.01369119201422578
  name "traffic cone(4)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -45.09325571434805 16.17027715248083 -0.004076718672611196
  rotation -0.0018886106391071932 0.9713631027516061 0.23759241520432983 0.01409439136220034
  name "traffic cone(5)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -44.43845240242648 4.634396070922502 0.2379757634545171
  rotation 0.061347629144771834 -0.9973115415799619 0.040076893962924055 1.9866825370261023
  name "traffic cone(6)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -44.75521518978133 -7.281196012499169 -0.004076718672611196
  rotation -0.006847527921556465 0.06959300246135203 0.9975519662502691 0.19609361815697546
  name "traffic cone(7)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.974044844171786 -53.76080002173635 -0.004076718672611196
  rotation -0.00027316454477513625 0.9999998489904718 0.0004768649337670819 0.013690792761860552
  name "traffic cone(8)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.96964484417179 -51.43500002173635 -0.004076718672611196
  rotation -0.0002731645447751101 0.9999998489904718 0.00047686493376708646 0.013690792761860552
  name "traffic cone(9)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.95354484417179 -48.95440002173635 -0.004076718672611196
  rotation -0.00027316454477512063 0.9999998489904718 0.00047686493376712424 0.013690792761860552
  name "traffic cone(10)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 33.717144844171784 -45.723400021736346 -0.004076718672611196
  rotation -0.00027316454477513625 0.9999998489904718 0.0004768649337670819 0.013690792761860552
  name "traffic cone(11)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -35.365382020768514 -30.22628667971297 -0.004076718672611196
  rotation 0.0027487037779008647 0.8994585051182058 -0.43699753111209205 0.015221105185739216
  name "traffic cone(12)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -42.35968739133966 -18.604513720745533 -0.004076718672611196
  rotation 0.006761846693791022 0.12149112337440181 -0.9925694859154777 0.1125740615476049
  name "traffic cone(13)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -26.685755155830158 -38.23720002173635 -0.004076718672611196
  rotation -0.0002731645447751493 0.9999998489904718 0.00047686493376991856 0.013690792761860552
  name "traffic cone(14)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -13.72645515583693 -43.78640002173635 -0.004076718672611196
  rotation -0.00027316454477524364 0.9999998489904718 0.0004768649337663011 0.013690792761860552
  name "traffic cone(15)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation -1.3851451558335999 -44.86730002173635 -0.004076718672611196
  rotation -0.0002731645447751716 0.9999998489904718 0.0004768649337730078 0.013690792761860552
  name "traffic cone(16)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 11.217444844163069 -44.895500021736346 -0.004076718672611196
  rotation -0.00027316454477524364 0.9999998489904718 0.0004768649337663011 0.013690792761860552
  name "traffic cone(17)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  translation 22.15304484416984 -44.815200021736345 -0.004076718672611196
  rotation -0.00027316454477517923 0.9999998489904718 0.0004768649337698474 0.013690792761860552
  name "traffic cone(18)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 48.100535 -116.28367 0
  rotation 0 0 1 2.61799
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
HighwayPole {
  translation -17.67 -117.85 0
  rotation 0 0 -1 3.14159
  height 9
  length 12
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -4.56 0
      name "horizontal highway sign"
      height 4
      length 5.5
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_bayonne.jpg"
      ]
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      name "vertical highway sign"
      height 2.5
      length 3
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_sebastian.jpg"
      ]
    }
  ]
}
OilBarrel {
  translation -110.31683804186741 -66.87515193336323 0.5998773750000002
  rotation -3.7902692438921347e-19 3.323011400457174e-19 1 3.1415899428493104
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -107.04991 -33.037806 0.5998773750000002
  rotation -4.195682419839141e-19 5.394913051959997e-19 1 3.141589999999999
  name "oil barrel(1)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -104.02120618362873 -54.209418285052934 0.3997547500000001
  rotation -0.6105155016818267 0.3689334954710653 0.7008272955055966 2.877194855847606
  name "oil barrel(2)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -44.99999806495841 -105.10399412769972 0.599877375
  rotation -1.1382490776104314e-18 3.4587111812532166e-20 -0.9999999999999999 1.5707953071795895
  name "oil barrel(3)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -21.3988 -45.2699 0.5998773750000002
  rotation 2.8231038246061655e-20 8.873508768140506e-19 -1 1.1781
  name "oil barrel(4)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -22.9161 -45.3401 0.5998773750000002
  rotation 8.69079404768499e-19 1.5276797184450653e-19 1 1.9634953071795869
  name "oil barrel(5)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -22.1326 -45.7229 0.5998773750000002
  rotation 0.0006193140220749592 -0.00035754443650632827 -0.9999997443060262 2.960346963795912e-15
  name "oil barrel(6)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
OilBarrel {
  translation -48.63923764203378 20.190364414738802 0.5998773750000002
  rotation -1.7149106502977282e-19 -1.0758269628498725e-19 1 3.010669606941611
  name "oil barrel(7)"
  height 1.2
  radius 0.4
  physics Physics {
    density -1
    mass 100
    damping Damping {
    }
  }
}
Crossroad {
  translation -4.5001488 105 0
  id "18"
  shape []
  connectedRoadIDs [
    "15"
"14"
  ]
}
DEF CAR2 BmwX5Simple {
  translation 32.07 94.71 0.4
  name "vehicle(1)"
}
Crossroad {
  translation 64.499851 105 0
  name "crossroad(1)"
  id "19"
  shape []
  connectedRoadIDs [
    "13"
"14"
  ]
}
Crossroad {
  translation 104.99978 64.500074 0
  name "crossroad(2)"
  id "20"
  shape []
  connectedRoadIDs [
    "12"
"13"
  ]
}
Crossroad {
  translation 105 -4.4999256 0
  name "crossroad(3)"
  id "21"
  shape []
  connectedRoadIDs [
    "11"
"12"
  ]
}
Crossroad {
  translation 4.5 -104.99975 0
  name "crossroad(4)"
  id "22"
  shape []
  connectedRoadIDs [
    "3"
"4"
  ]
}
Crossroad {
  translation -64.5 -105 0
  name "crossroad(5)"
  id "23"
  shape []
  connectedRoadIDs [
    "2"
"3"
  ]
}
Crossroad {
  translation -104.99987 -64.499926 0
  name "crossroad(6)"
  id "24"
  shape []
  connectedRoadIDs [
    "1"
"2"
  ]
}
Crossroad {
  translation -105 4.4999794 0
  name "crossroad(7)"
  id "25"
  shape []
  connectedRoadIDs [
    "0"
"1"
  ]
}
Crossroad {
  translation -44.999865 -4.4999256 0
  name "crossroad(8)"
  id "26"
  shape []
  connectedRoadIDs [
    "8"
"9"
  ]
}
Crossroad {
  translation -4.5 -45 0
  name "crossroad(9)"
  id "27"
  shape []
  connectedRoadIDs [
    "9"
"10"
  ]
}
Crossroad {
  translation 45 4.5000744 0
  name "crossroad(10)"
  id "28"
  shape []
  connectedRoadIDs [
    "6"
"5"
  ]
}
Crossroad {
  translation 4.4998512 45.00011 0
  name "crossroad(11)"
  id "29"
  shape []
  connectedRoadIDs [
    "7"
"6"
  ]
}
ResidentialTower {
  translation 0 135.47 0
}
DEF WEBOTS_VEHICLE0 chitu {
  translation 22.951039035651547 97.09619696899792 0.2792532123124251
  rotation -0.06514470546961697 0.06269703048957143 0.9959042372221671 0.1566930359294113
  extensionSlot [
    Group {
      children [
        Accelerometer {
        }
        Gyro {
        }
        InertialUnit {
        }
      ]
    }
    VelodyneVLP-16 {
      translation 2.3 0 1.65
      rotation 0 0 1 -1.5707
      name "laser_top"
    }
    Lidar {
      numberOfLayers 16
      maxRange 100
      type "rotating"
    }
    Camera {
      translation 2.5 0 1.5
      width 640
      height 480
    }
    Transform {
      translation 1.05 0 -0.25
      children [
        Shape {
          appearance Plastic {
          }
          geometry Mesh {
            url [
              "../protos/chitu_coma_rotate.dae"
            ]
          }
          castShadows FALSE
        }
      ]
    }
  ]
}
LargeResidentialTower {
  translation 69.63 60.81 0
  rotation 0 1 0 0
  name "residential tower(1)"
}
ComposedHouse {
  translation 39.7913 131.969 0
  rotation 0 0 1 1.8326
}
Robot {
  name "supervisor"
  controller "supervisor_control"
  supervisor TRUE
}
