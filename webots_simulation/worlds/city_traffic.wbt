#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/CurvedRoadSegment.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"
EXTERNPROTO "../protos/RoadIntersection.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CrossRoadsTrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BuildingUnderConstruction.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CommercialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/UBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/HollowBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Hotel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/TheThreeTowers.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/CyberboticsTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BigGlassTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Auditorium.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/Museum.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/FastFoodRestaurant.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/SimpleBuilding.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/Forest.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/trees/protos/SimpleTree.proto"
EXTERNPROTO "../protos/PedestrianCrossing.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/CautionPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/OrderPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/StopSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/YieldSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitSign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/SpeedLimitPanel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficCone.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/advertising_board/protos/AdvertisingBoard.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwayPole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/HighwaySign.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Crossroad.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/bmw/BmwX5Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/citroen/CitroenCZeroSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/toyota/ToyotaPriusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/lincoln/LincolnMKZSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/range_rover/RangeRoverSportSVRSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/tesla/TeslaModel3Simple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/mercedes_benz/MercedesBenzSprinterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/BusSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTrailerSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/TruckTankSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/ScooterDriver.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeSimple.proto"
IMPORTABLE EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/generic/MotorbikeDriver.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/Pole.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficLight.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/TrafficLightStandardLampGeometry.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/LargeResidentialTower.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/BungalowStyleHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ComposedHouse.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/buildings/protos/ResidentialBuildingWithRoundFront.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation 0.5016667362128198 -0.29966999556981133 -0.8114976152354154 3.9192445191851166
  position -38.2674246491668 -6.115395443607987 143.5434174296739
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
SumoInterface {
  gui FALSE
  maxVehicles 10
  children [
    DEF TRAFFIC_26 Pole {
      translation -44.99 44.9099 0
      rotation 0 0 -1 0
      name "pole26"
      slot [
        DEF TLS_26_0 TrafficLight {
          translation 0 0.1 0
          rotation 0 0 1 1.57
          name "traffic light(1)"
          red_light "26_0_r"
          orange_light "26_0_y"
          green_light "26_0_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
        DEF TLS_26_12 TrafficLight {
          translation 0 -0.1 0
          rotation 0 0 1 -1.57
          name "traffic light(2)"
          red_light "26_12_r"
          orange_light "26_12_y"
          green_light "26_12_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
        DEF TLS_26_6 TrafficLight {
          translation 0.1 0 0
          name "traffic light(3)"
          red_light "26_6_r"
          orange_light "26_6_y"
          green_light "26_6_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
        DEF TLS_26_16 TrafficLight {
          translation -0.1 0 0
          rotation 0 0 1 3.1415
          red_light "26_16_r"
          orange_light "26_16_y"
          green_light "26_16_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
      ]
    }
    DEF TRAFFIC_25 Pole {
      translation 45 -45 0
      rotation 0 0 -1 0
      name "pole25"
      slot [
        DEF TLS_25_0 TrafficLight {
          translation 0 0.1 0
          rotation 0 0 1 1.57
          name "traffic light(1)"
          red_light "25_0_r"
          orange_light "25_0_y"
          green_light "25_0_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
        DEF TLS_25_12 TrafficLight {
          translation 0 -0.1 0
          rotation 0 0 1 -1.57
          name "traffic light(2)"
          red_light "25_12_r"
          orange_light "25_12_y"
          green_light "25_12_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
        DEF TLS_25_6 TrafficLight {
          translation 0.1 0 0
          name "traffic light(3)"
          red_light "25_6_r"
          orange_light "25_6_y"
          green_light "25_6_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
        DEF TLS_25_16 TrafficLight {
          translation -0.1 0 0
          rotation 0 0 1 3.1415
          red_light "25_16_r"
          orange_light "25_16_y"
          green_light "25_16_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
      ]
    }
    DEF TRAFFIC_27 Pole {
      translation 104.96 92.99 0
      rotation 0 0 -1 0
      name "pole27"
      slot [
        DEF TLS_27_0 TrafficLight {
          translation 0 0.1 0
          rotation 0 0 1 1.57
          name "traffic light(1)"
          red_light "27_0_r"
          orange_light "27_0_y"
          green_light "27_0_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
        DEF TLS_27_12 TrafficLight {
          translation 0 -0.1 0
          rotation 0 0 1 -1.57
          name "traffic light(2)"
          red_light "27_12_r"
          orange_light "27_12_y"
          green_light "27_12_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            1 0 0
          ]
        }
        DEF TLS_27_6 TrafficLight {
          translation 0.1 0 0
          name "traffic light(3)"
          red_light "27_6_r"
          orange_light "27_6_y"
          green_light "27_6_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
        DEF TLS_27_16 TrafficLight {
          translation -0.1 0 0
          rotation 0 0 1 3.1415
          red_light "27_16_r"
          orange_light "27_16_y"
          green_light "27_16_g"
          lamp_geometry TrafficLightStandardLampGeometry {
          }
          recognitionColors [
            0.25 0.25 0.25
            0 1 0
          ]
        }
      ]
    }
  ]
}
DEF GROUND Solid {
  boundingObject DEF GROUND_PLANE Plane {
    size 2000 2000
  }
  locked TRUE
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
CurvedRoadSegment {
  translation -64.5 4.5 0.02
  rotation 0 0 1 1.5708
  id "0"
  startJunction "40"
  endJunction "26"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -105 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(1)"
  id "1"
  startJunction "40"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation -64.5 -64.5 0.02
  rotation 0 0 1 3.14156
  name "road(2)"
  id "2"
  startJunction "38"
  endJunction "39"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -64.5 -105 0.02
  name "road(3)"
  id "3"
  startJunction "38"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
CurvedRoadSegment {
  translation 4.5 -64.5 0.02
  rotation 0 0 1 -1.5708
  name "road(4)"
  id "4"
  startJunction "25"
  endJunction "37"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
RoadIntersection {
  translation 45 -45 0.02
  rotation 0 0 1 0.785398
  id "25"
  connectedRoadIDs [
    "9"
    "10"
    "4"
    "5"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
StraightRoadSegment {
  translation 45 4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(5)"
  id "5"
  startJunction "43"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 4.5 4.5 0.02
  name "road(6)"
  id "6"
  startJunction "44"
  endJunction "43"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -25.5 45 0.02
  name "road(7)"
  id "7"
  startJunction "26"
  endJunction "44"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
RoadIntersection {
  translation -45 45 0.02
  rotation 0 0 1 0.785398
  name "road intersection(1)"
  id "26"
  connectedRoadIDs [
    "7"
    "23"
    "0"
    "24"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
RoadIntersection {
  translation 105 93 0.02
  rotation 0 0 1 0.785398
  name "road intersection(2)"
  id "27"
  connectedRoadIDs [
    "12"
    "20"
    "17"
    "16"
  ]
  roadsWidth 21.5
  startRoadsLength [
    8.75
  ]
  startRoadsNumberOfLanes 4
  startRoadsStartLine [
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
    "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png"
  ]
  startRoadsLine [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  startRoadBorder FALSE
}
CurvedRoadSegment {
  translation -4.5 -4.5 0.02
  rotation 0 0 1 3.14156
  name "road(8)"
  id "8"
  startJunction "42"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -4.5 -45 0.02
  name "road(9)"
  id "9"
  startJunction "42"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
CurvedRoadSegment {
  translation 64.5 -4.5 0.02
  rotation 0 0 1 -1.5708
  name "road(10)"
  id "10"
  startJunction "45"
  endJunction "25"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 75 -97.5 0.02
  rotation 0 0 1 -1.5708
  name "road(11)"
  id "11"
  startJunction "35"
  endJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 90
}
StraightRoadSegment {
  translation 85.5 93 0.02
  rotation 0 0 1 3.14156
  name "road(12)"
  id "12"
  startJunction "27"
  endJunction "28"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 9
}
StraightRoadSegment {
  translation 75 -187.5 0.02
  rotation 0 0 -1 3.14156
  name "road(13)"
  id "13"
  startJunction "36"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 250
}
StraightRoadSegment {
  translation 165 52.5 0.02
  rotation 0 0 1 -1.5708
  name "road(14)"
  id "14"
  startJunction "34"
  endJunction "35"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 150
}
StraightRoadSegment {
  translation 64.5 231 0.02
  rotation 0 0 1 3.14156
  name "road(15)"
  id "15"
  startJunction "32"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 200
}
StraightRoadSegment {
  translation 105 112.5 0.02
  rotation 0 0 1 1.57079
  name "road(16)"
  id "16"
  startJunction "27"
  endJunction "33"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
StraightRoadSegment {
  translation 105 -4.5 0.02
  rotation 0 0 1 1.57079
  name "road(17)"
  id "17"
  startJunction "45"
  endJunction "27"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 78
}
CurvedRoadSegment {
  translation 76.5 133.5 0.02
  rotation 0 0 -1 3.14156
  name "road(18)"
  id "18"
  startJunction "28"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 64.5 190.5 0.02
  name "road(19)"
  id "19"
  startJunction "32"
  endJunction "33"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation 124.5 52.5 0.02
  name "road(20)"
  id "20"
  startJunction "27"
  endJunction "34"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  name "road(21)"
  id "21"
  startJunction "30"
  endJunction "29"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
CurvedRoadSegment {
  translation -4.5 133.5 0.02
  rotation 0 0 1 1.5708
  name "road(22)"
  id "22"
  startJunction "31"
  endJunction "30"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  curvatureRadius 40.5
}
StraightRoadSegment {
  translation -45 25.5 0.02
  rotation 0 0 -1 1.5708
  name "road(23)"
  id "23"
  startJunction "26"
  endJunction "41"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 30
}
StraightRoadSegment {
  translation -45 64.5 0.02
  rotation 0 0 1 1.5708
  name "road(24)"
  id "24"
  startJunction "26"
  endJunction "31"
  width 21.5
  numberOfLanes 4
  numberOfForwardLanes 2
  lines [
    RoadLine {
    }
    RoadLine {
      color 0.85 0.75 0.3
      type "double"
    }
  ]
  rightBorder FALSE
  leftBorder FALSE
  rightBarrier TRUE
  leftBarrier TRUE
  length 69
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden position_0_0 -1.3029545139270251e-11
  hidden position2_0_0 0.0061346705669825955
  hidden position_0_1 1.2990339280338977e-11
  hidden position2_0_1 -0.0062369408179208975
  hidden position_0_2 -0.8881298848913315
  hidden position_0_3 -0.8974702922079971
  hidden linearVelocity_0 -4.1284044793585504e-13 -2.4942293157308816e-14 3.2505464048317183e-12
  hidden angularVelocity_0 8.47685510527566e-13 -2.198391193982816e-12 6.497119466650298e-15
  hidden rotation_1 6.5147725696351254e-12 -1 2.1239126264329324e-09 0.006134670566980634
  hidden linearVelocity_1 -5.065239634816581e-13 -1.5920015177418297e-13 1.2756428923028246e-14
  hidden angularVelocity_1 8.262885649081143e-13 -2.1352827750660555e-12 6.3894998320838804e-15
  hidden rotation_2 -6.495169640169488e-12 -0.9999999999999999 -2.0827994925416666e-09 0.006236940817937227
  hidden linearVelocity_2 -4.3313779984339937e-13 -2.4730249088702833e-13 1.4132192947411132e-14
  hidden angularVelocity_2 8.187860490589848e-13 -2.116875383019466e-12 6.4436168507575125e-15
  hidden rotation_3 0 1 0 0.8881298848913315
  hidden linearVelocity_3 -1.8149480282710321e-13 -3.7142878124109636e-14 1.3853930954644746e-13
  hidden angularVelocity_3 2.2673517009705897e-13 -4.710030374338317e-13 3.1006213789431725e-15
  hidden rotation_4 0 -1 0 0.8974702922079973
  hidden linearVelocity_4 -4.5105159244351013e-14 -1.0266111799502262e-13 1.4034934055998794e-13
  hidden angularVelocity_4 1.0013111173099539e-13 -1.1920018880169195e-13 3.094216948024833e-15
  translation -85.4726495331165 45.63100704699609 0.2570972429955831
  rotation 0.0005090649871355624 8.873333989173001e-05 0.9999998664896077 -2.7964455870561284
}
CrossRoadsTrafficLight {
  translation 45 -45 0
}
DEF STONES Solid {
  translation 5.03891 -136.158 -4.23581
  children [
    DEF STONES_GROUP Group {
      children [
        Pose {
          translation 0 2 -0.6
          children [
            Shape {
              appearance DEF OBJECTS_APPEARANCE PBRAppearance {
                baseColor 0.5 0.5 0.5
                roughness 1
                metalness 0
              }
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0.5 -3.5 -0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 4 2 -0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 6 -1 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 9 0 0.15
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 5.5 -5 0.2
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 0 0 0.05
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 10 5 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 1 6 0.3
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13 -4 0
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
        Pose {
          translation 13.5 1.5 0.4
          children [
            Shape {
              appearance USE OBJECTS_APPEARANCE
              geometry Sphere {
                radius 5
                subdivision 2
              }
            }
          ]
        }
      ]
    }
  ]
  name "solid(2)"
  boundingObject USE STONES_GROUP
}
BuildingUnderConstruction {
  translation 78.5008 143.08 0
}
CommercialBuilding {
  translation 70.9574 31.6315 0
}
UBuilding {
  translation -87.1466 81.9927 0
}
UBuilding {
  translation -33.0931 -148.903 0
  rotation 0 0 1 1.5708
  name "U building(1)"
}
HollowBuilding {
}
Hotel {
  translation -9.97953 71.6228 0
}
LargeResidentialTower {
  translation -17.51 105.22 0
  numberOfFloors 12
}
BungalowStyleHouse {
  translation 19.65 99.5 0
}
TheThreeTowers {
  translation 68.118 -90.636 0
}
TheThreeTowers {
  translation -128.998 -89.456 0
  rotation 0 0 1 0.523599
  name "three towers(1)"
}
CyberboticsTower {
  translation 27.5187 68.7504 0
}
BigGlassTower {
  translation 68.6779 -9.29537 0
  rotation 0 0 1 1.5708
}
Auditorium {
  translation -63.9296 -61.9719 0
  rotation 0 0 1 0.654496
}
Auditorium {
  translation 148.81 132.46 0
  rotation 0 0 1 -3.0106953071795863
  name "auditorium(1)"
}
Museum {
  translation -0.191182 -68.6571 0
  rotation 0 0 1 1.5708
}
Museum {
  translation 110.334 -65.2535 0
  rotation 0 0 1 2.0944
  name "museum(1)"
}
ComposedHouse {
  translation -26.35 0 0
}
ResidentialBuildingWithRoundFront {
  translation 33.5652 -136.667 0
  rotation 0 0 1 1.8326
  name "residential building(1)"
}
ResidentialBuilding {
  translation -69.274 -1.81329 0
}
FastFoodRestaurant {
  translation 63.46 212.26 0
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
FastFoodRestaurant {
  translation 48.60864 48.933131 0
  rotation 0 0 -1 -2.8797953071795863
  name "fast food restaurant(1)"
  height 4.3
  length 13
  width 12
  brand "subway"
  numberOfSides 2
}
SimpleBuilding {
  translation 46.9364 208.338 0
  wallType "glass highrise"
}
SimpleBuilding {
  translation 66.8317 195.97 0
  name "building(1)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 97.8047 -149.501 0
  name "building(2)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 117.415 -135.778 0
  name "building(3)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 126.106 -115.963 0
  name "building(4)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -134.064 -16.533 0
  name "building(14)"
  floorHeight 5
  floorNumber 6
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 139.996 -95.9459 0
  name "building(5)"
  floorHeight 5
  wallType "brick building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation 78.2778 175.999 0
  name "building(6)"
  wallType "glass highrise"
}
SimpleBuilding {
  translation 57.9852 259.763 0
  name "building(7)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "classic building"
}
SimpleBuilding {
  translation -53.2255 258.571 0
  name "building(8)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
}
SimpleBuilding {
  translation -56.5761 182.166 0
  rotation 0 0 -1 2.618
  name "building(9)"
  floorHeight 6
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  groundFloorScale [
    0
  ]
  roofType "old tiles"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -77.6413 142.999 0
  rotation 0 0 1 3.14159
  name "building(2)"
  floorNumber 6
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 20
    0 20
  ]
  wallType "gray glass building"
  roofType "gravel"
  roofShape "flat roof"
}
SimpleBuilding {
  translation -72.4649 -133.213 0
  rotation 0 0 1 5.23599
  name "building(10)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -131.19 52.9749 0
  rotation 0 0 1 -2.8797853071795863
  name "building(15)"
  floorNumber 2
  corners [
    0 0
    -10 0
    -10 -10
    10 -10
    10 10
    0 10
  ]
  wallType "highrise"
}
SimpleBuilding {
  translation -22.0181 258.921 0
  name "building(11)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "factory building"
}
SimpleBuilding {
  translation 20.4483 259.676 0
  name "building(12)"
  floorNumber 2
  corners [
    5 5
    5 -5
    -5 -5
    -5 5
  ]
  wallType "tall house"
}
SimpleBuilding {
  translation -3.91795 144.18 0
  name "building(13)"
  wallType "residential building"
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -105.8 -117
    -99 -150.8
    -69.7 -174
    -186 -176.3
    -205.3 -140.5
    -195 -117
  ]
  density 0.3
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -80 -118
    -73.7 -141
    -49 -153
    64.3 -153
    12 -117
  ]
}
Forest {
  rotation 0 0 1 -1.5608
  shape [
    -7.6 116.2
    72.4 116.5
    174.5 99.2
    175.8 191.2
    -56.5 122.9
  ]
}
SimpleTree {
  translation -86.3803 -3.69635 0
}
SimpleTree {
  translation -86.3803 -18.9264 0
  name "tree(1)"
}
SimpleTree {
  translation -43.078 -123.055 0
  name "tree(2)"
}
SimpleTree {
  translation -22.318 -123.055 0
  name "tree(3)"
}
SimpleTree {
  translation 1.70201 -123.055 0
  name "tree(4)"
}
SimpleTree {
  translation 20.4431 130.5 0
  name "tree(5)"
}
SimpleTree {
  translation 53.4842 123.995 0
  rotation 0 0 1 5.49779
  name "tree(6)"
}
SimpleTree {
  translation 83.0494 196.584 0
  name "tree(7)"
}
SimpleTree {
  translation -33.9112 179.976 0
  rotation 0 -1 0 6.28319
  name "tree(8)"
}
SimpleTree {
  translation -59.8734 114.947 0
  name "tree(9)"
}
SimpleTree {
  translation -22.2335 150.308 0
  rotation 0 -1 0 6.28319
  name "tree(10)"
}
SimpleTree {
  translation 16.4343 145.045 0
  name "tree(11)"
}
SimpleTree {
  translation 89.3742 77.3517 0
  name "tree(12)"
}
SimpleTree {
  translation 89.3742 59.8317 0
  name "tree(13)"
}
SimpleTree {
  translation 89.3742 42.5017 0
  name "tree(14)"
}
SimpleTree {
  translation 89.3742 24.6817 0
  name "tree(15)"
}
SimpleTree {
  translation 89.3742 6.71175 0
  name "tree(16)"
}
SimpleTree {
  translation 89.3742 -10.6583 0
  name "tree(17)"
}
SimpleTree {
  translation 137.36 -53.2389 0
  name "tree(18)"
}
SimpleTree {
  translation 122.111 -32.1201 0
  name "tree(19)"
}
SimpleTree {
  translation 110.762 -29.023 0
  name "tree(20)"
}
SimpleTree {
  translation 90.773 -54.456 0
  name "tree(21)"
}
SimpleTree {
  translation 40.292 -105.225 0
  name "tree(22)"
}
SimpleTree {
  translation -29.8047 -59.5626 0
  name "tree(23)"
}
SimpleTree {
  translation -56.111 -34.7085 0
  name "tree(24)"
}
SimpleTree {
  translation -86.3803 -38.0164 0
  name "tree(25)"
}
SimpleTree {
  translation -86.3803 9.02365 0
  name "tree(26)"
}
PedestrianCrossing {
  translation 45 -26 -0.06
  rotation 0 0 1 -3.1415853071795863
}
PedestrianCrossing {
  translation -61.4608 45.0693 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(1)"
}
PedestrianCrossing {
  translation 26.9799 -45.1201 -0.06
  rotation 0 0 1 -1.5707853071795865
  name "pedestrian crossing(2)"
}
CautionSign {
  translation -91.9275 48.9391 0
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_left.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 45.9944 -94.6291 0
  rotation 0 0 1 -2.4871
  name "caution sign(1)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/pedestrian_crossing.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 8.87679 55.4925 0
  name "caution sign(2)"
}
CautionSign {
  translation 33.842 10.5534 0
  rotation 0 0 1 1.7017
  name "caution sign(3)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/bump.jpg"
      ]
    }
  ]
}
CautionSign {
  translation 84.1154 -26.9475 0
  rotation 0 0 1 0.6545
  name "caution sign(4)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/cross_roads.jpg"
      ]
    }
  ]
}
CautionSign {
  translation -5.43669 -34.1146 0
  rotation 0 0 1 -0.5236
  name "caution sign(5)"
  signBoards [
    CautionPanel {
      translation 0 0 -0.17
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/turn_right.jpg"
      ]
    }
  ]
}
OrderSign {
  translation -67.6589 34.4983 0
  rotation 0 0 1 3.14159
  signBoards [
    OrderPanel {
      translation 0.026 0 -0.175
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/no_right_turn.jpg"
      ]
    }
  ]
}
StopSign {
  translation -34.6012 34.2884 0
  rotation 0 0 1 -1.5708
}
YieldSign {
  translation -55.468 66.4958 0
  rotation 0 0 1 1.5708
}
SpeedLimitSign {
  translation -113.192 20.4404 0
  rotation 0 0 1 0.9163
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 26.6277 -84.4244 0
  rotation 0 0 1 0.6545
  name "speed limit(1)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_55.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation -30.3528 79.1341 0
  rotation 0 0 1 -2.3562
  name "speed limit(2)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 87.1538 -50.335 0
  rotation 0 0 1 -3.14159
  name "speed limit(3)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/speed_limit_65.jpg"
      ]
    }
  ]
}
SpeedLimitSign {
  translation 31.0289 -34.4459 0
  name "speed limit(4)"
  signBoards [
    SpeedLimitPanel {
      translation 0.023 0 0
      signImage [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/signs/us/one_way_sign_left.jpg"
      ]
    }
  ]
}
TrafficCone {
  hidden linearVelocity_0 2.4403880121321905e-16 1.2902260914728179e-15 -3.1619167275795757e-15
  hidden angularVelocity_0 -7.824932807937675e-15 1.3820344777954013e-15 7.471512323570179e-17
  translation 41.18160852334033 -54.86909037698833 0.2379757634545171
  rotation 0.7068402576257298 0.535855374116409 0.46177469422885836 -2.4681182771203916
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -4.4029367480309354e-16 -1.0425044657916453e-15 -2.1138645307656617e-15
  hidden angularVelocity_0 6.328922866724821e-15 -2.628949396556702e-15 3.818248259694076e-17
  translation 33.72154992745149 -50.63667696602178 0.23797576345451715
  rotation -0.442360396011865 0.8490005570824324 -0.28899019726271913 -2.12878475773313
  name "traffic cone(1)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -1.5064089225254048e-15 4.752287502056674e-15 -7.549515143216642e-15
  hidden angularVelocity_0 -2.8745414068364184e-14 -9.110571211561798e-15 -1.463927092229982e-19
  translation 28.07349572185293 -45.481782852830705 0.035923281327389034
  rotation 0.006842457951436518 0.009534634183677145 -0.999931133388778 1.2455423434081978
  name "traffic cone(2)"
  physics Physics {
    density -1
    mass 1
  }
}
TrafficCone {
  hidden linearVelocity_0 -4.7956990575788174e-15 -2.0909507240517837e-19 -7.567282136737061e-15
  hidden angularVelocity_0 2.990462204244704e-18 -2.900791618984006e-14 8.461161682437013e-19
  translation 33.71714525037682 -45.72340002635626 -0.004076718672610974
  rotation -0.0002731772321864616 0.999999848101453 0.0004787183624656655 0.013690792773986203
  name "traffic cone(3)"
  physics Physics {
    density -1
    mass 1
  }
}
AdvertisingBoard {
  translation 48.100535 -116.28367 0
  rotation 0 0 1 2.61799
  backTexture []
  displayBackLight TRUE
  displayWidth 8
  displayHeight 6
  frameThickness 0.9
  frameColor 0.52549 0.52549 0.52549
  poleNumber 2
  poleHeight 11
  baseRatio 5.8
}
HighwayPole {
  translation -17.67 -117.85 0
  rotation 0 0 -1 3.14159
  height 9
  length 12
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -4.56 0
      height 4
      length 5.5
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_bayonne.jpg"
      ]
    }
  ]
  rightVerticalSigns [
    HighwaySign {
      name "highway sign(1)"
      height 2.5
      length 3
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_sebastian.jpg"
      ]
    }
  ]
}
HighwayPole {
  translation 118.15 72.97 0
  rotation 0 0 1 -1.5707953071795862
  name "highway pole(1)"
  stand 2
  height 8
  length 25
  thickness 0.3
  curveRadius 0.5
  rightHorizontalSigns [
    HighwaySign {
      translation 0 -3.95 0
      height 4
      length 5.5
      color 0.6 0.6 0.6
    }
  ]
  rightVerticalSigns []
  leftVerticalSigns [
    HighwaySign {
      translation 0 0 0.58
      name "highway sign(1)"
      height 2.5
      length 3
      color 0.6 0.6 0.6
      texture [
        "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/highway_sign_la_ciotat.jpg"
      ]
    }
  ]
}
Crossroad {
  translation 76.500107 93 0
  id "28"
  shape []
  connectedRoadIDs [
    "18"
    "12"
  ]
}
Crossroad {
  translation 36 133.50015 0
  name "crossroad(1)"
  id "29"
  shape []
  connectedRoadIDs [
    "21"
    "18"
  ]
}
Crossroad {
  translation -4.500405 174 0
  name "crossroad(2)"
  id "30"
  shape []
  connectedRoadIDs [
    "22"
    "21"
  ]
}
Crossroad {
  translation -45.000035 133.49978 0
  name "crossroad(3)"
  id "31"
  shape []
  connectedRoadIDs [
    "24"
    "22"
  ]
}
Crossroad {
  translation 64.499851 231 0
  name "crossroad(4)"
  id "32"
  shape []
  connectedRoadIDs [
    "15"
    "19"
  ]
}
Crossroad {
  translation 104.99975 190.50007 0
  name "crossroad(5)"
  id "33"
  shape []
  connectedRoadIDs [
    "16"
    "19"
  ]
}
Crossroad {
  translation 165 52.500074 0
  name "crossroad(6)"
  id "34"
  shape []
  connectedRoadIDs [
    "20"
    "14"
  ]
}
Crossroad {
  translation 165.00028 -97.499835 0
  name "crossroad(7)"
  id "35"
  shape []
  connectedRoadIDs [
    "11"
    "14"
  ]
}
Crossroad {
  translation 75 -187.5 0
  name "crossroad(8)"
  id "36"
  shape []
  connectedRoadIDs [
    "13"
    "11"
  ]
}
Crossroad {
  translation 4.5 -104.99975 0
  name "crossroad(9)"
  id "37"
  shape []
  connectedRoadIDs [
    "3"
    "4"
  ]
}
Crossroad {
  translation -64.5 -105 0
  name "crossroad(10)"
  id "38"
  shape []
  connectedRoadIDs [
    "2"
    "3"
  ]
}
Crossroad {
  translation -104.99987 -64.499926 0
  name "crossroad(11)"
  id "39"
  shape []
  connectedRoadIDs [
    "1"
    "2"
  ]
}
Crossroad {
  translation -105 4.4999794 0
  name "crossroad(12)"
  id "40"
  shape []
  connectedRoadIDs [
    "0"
    "1"
  ]
}
Crossroad {
  translation -45.000015 -4.4999256 0
  name "crossroad(13)"
  id "41"
  shape []
  connectedRoadIDs [
    "23"
    "8"
  ]
}
Crossroad {
  translation -4.5 -45 0
  name "crossroad(14)"
  id "42"
  shape []
  connectedRoadIDs [
    "9"
    "8"
  ]
}
Crossroad {
  translation 45 4.5000744 0
  name "crossroad(15)"
  id "43"
  shape []
  connectedRoadIDs [
    "5"
    "6"
  ]
}
Crossroad {
  translation 4.4998512 45.00011 0
  name "crossroad(16)"
  id "44"
  shape []
  connectedRoadIDs [
    "7"
    "6"
  ]
}
Crossroad {
  translation 105 -4.4999256 0
  name "crossroad(17)"
  id "45"
  shape []
  connectedRoadIDs [
    "10"
    "17"
  ]
}
DEF SUMO_VEHICLE0 Solid {
  translation 96.81979345421996 191.49942507984534 0.358
  rotation 0 0 -1 1.5361345021613606
  children [
    LincolnMKZSimple {
      translation -2.85 0 0
      color 0.18 0.28 0.64
      recognitionColors [
        0.18 0.28 0.64
      ]
    }
  ]
  name "0"
  linearVelocity 1.0145357223210283 -12.843386977512095 0
  angularVelocity 0 0 -0.32618503148464484
}
DEF SUMO_VEHICLE1 Solid {
  translation 96.93 17.91112930770053 0.358
  rotation 0 0 -1 1.5707963267948963
  children [
    RangeRoverSportSVRSimple {
      translation -2.85 0 0
      color 0.14 0.29 0.16
      recognitionColors [
        0.14 0.29 0.16
      ]
    }
  ]
  name "1"
  linearVelocity 0 -12.986779724962075 0
}
DEF SUMO_VEHICLE2 Solid {
  translation 108.18630507116183 -25.5259572802307 0.4
  rotation 0 0 1 1.1245449310147086
  children [
    MercedesBenzSprinterSimple {
      translation -2.85 0 0
      color 0.85 0.85 0.05
      recognitionColors [
        0.85 0.85 0.05
      ]
    }
  ]
  name "2"
  linearVelocity 6.248978896997314 11.851000853672764 0
  angularVelocity 0 0 0.2913581422898348
}
DEF SUMO_VEHICLE3 Solid {
  translation 26.83056353458735 -184.81192225392067 0.36
  rotation 0 0 -1 3.141552631176237
  children [
    TeslaModel3Simple {
      translation -2.85 0 0
      color 0.72 0.52 0.18
      recognitionColors [
        0.72 0.52 0.18
      ]
    }
  ]
  name "3"
  linearVelocity -12.199117838231928 -0.00048823812690557133 0
  angularVelocity 0 0 -4.440892098500626e-15
}
DEF SUMO_VEHICLE4 Solid {
  translation -42.31 105.72623529465494 0.36
  rotation 0 0 1 1.5707963267948968
  children [
    TeslaModel3Simple {
      translation -2.85 0 0
      color 0.1 0.15 0.18
      recognitionColors [
        0.1 0.15 0.18
      ]
    }
  ]
  name "4"
  linearVelocity 0 12.109375597061103 0
}
DEF SUMO_VEHICLE5 Solid {
  translation 159.11079295671246 67.3593186050079 0.4
  rotation 0 0 -1 1.167574180004344
  children [
    MercedesBenzSprinterSimple {
      translation -2.85 0 0
      color 0.85 0.85 0.05
      recognitionColors [
        0.85 0.85 0.05
      ]
    }
  ]
  name "5"
  linearVelocity 6.038920310356559 -13.125924180718156 0
  angularVelocity 0 0 -0.4302711680893534
}
DEF SUMO_VEHICLE6 Solid {
  translation 17.935498813420274 -93.87306187233031 0.285
  rotation 0 0 -1 2.728883931620721
  children [
    CitroenCZeroSimple {
      translation -2.85 0 0
      color 0.18 0.28 0.64
      recognitionColors [
        0.18 0.28 0.64
      ]
    }
  ]
  name "6"
  linearVelocity -11.706373789704685 -5.800887085508109 0
  angularVelocity 0 0 -0.42833420024083857
}
DEF SUMO_VEHICLE7 Solid {
  translation -89.55532058348979 233.69 0.36
  rotation 0 0 1 3.141592653589793
  children [
    TeslaModel3Simple {
      translation -2.85 0 0
      color 0.85 0.85 0.05
      recognitionColors [
        0.85 0.85 0.05
      ]
    }
  ]
  name "7"
  linearVelocity -12.593423368875419 0 0
}
DEF SUMO_VEHICLE8 Solid {
  translation 96.06062315225175 -33.846615901824286 0.358
  rotation 0 0 1 0.8325492182644191
  children [
    RangeRoverSportSVRSimple {
      translation -2.85 0 0
      color 0.85 0.85 0.05
      recognitionColors [
        0.85 0.85 0.05
      ]
    }
  ]
  name "8"
  linearVelocity 11.056313343184597 11.096834230937418 0
  angularVelocity 0 0 0.35425342327589876
}
DEF SUMO_VEHICLE9 Solid {
  translation 107.69 108.70498984266801 0.358
  rotation 0 0 1 1.5730588977979598
  children [
    RangeRoverSportSVRSimple {
      translation -2.85 0 0
      color 0.72 0.52 0.18
      recognitionColors [
        0.72 0.52 0.18
      ]
    }
  ]
  name "9"
  linearVelocity 0 11.878303497839795 0
  angularVelocity 0 0 -0.07541903343540213
}
