#VRML_SIM R2023b utf8

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackground.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/backgrounds/protos/TexturedBackgroundLight.proto"
EXTERNPROTO "../protos/chitu.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/protos/SumoInterface.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/StraightRoadSegment.proto"

WorldInfo {
  ERP 0.6
  basicTimeStep 10
  lineScale 1
  contactProperties [
    ContactProperties {
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "CitroenCZeroWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "ToyotaPriusWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "LincolnMKZWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "RangeRoverSportSVRWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
    ContactProperties {
      material2 "TruckWheels"
      coulombFriction [
        8
      ]
      softCFM 1e-05
      bumpSound ""
      rollSound ""
      slideSound ""
    }
  ]
}
Viewpoint {
  orientation -0.46556201791373464 0.5867842201803014 0.6625226686110347 2.1406180267246873
  position 97.68368503012144 -78.96105797519729 491.67390909728323
  near 1
  follow "vehicle"
  lensFlare LensFlare {
  }
}
SumoInterface {
  gui FALSE
}
TexturedBackground {
}
TexturedBackgroundLight {
}
Fog {
  color 0.3607843137254902 0.3607843137254902 0.3607843137254902
  visibilityRange 1200
}
DEF GROUND_SHAPE Solid {
  translation 0 0 -0.02
  children [
    Shape {
      appearance PBRAppearance {
        baseColor 0.8 0.8 0.8
        baseColorMap ImageTexture {
          url [
            "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/worlds/textures/ground.jpg"
          ]
        }
        roughness 0.5
        metalness 0
        textureTransform TextureTransform {
          scale 500 500
        }
      }
      geometry DEF GROUND_PLANE Plane {
        size 2000 2000
      }
    }
  ]
  name "solid(1)"
  boundingObject USE GROUND_PLANE
  locked TRUE
}
DEF WEBOTS_VEHICLE0 chitu {
  hidden position_0_0 -1.424851952041345e-11
  hidden position2_0_0 6836.05088025916
  hidden position_0_1 1.0518164206921193e-11
  hidden position2_0_1 6892.564584522904
  hidden position_0_2 6729.412574051852
  hidden position_0_3 6807.716576541601
  hidden linearVelocity_0 -2.1518314777305112e-13 -5.154907957885001e-15 1.1975511040910785e-15
  hidden angularVelocity_0 8.868772427049458e-15 -1.1043763814406557e-14 2.046837273497519e-14
  hidden translation_1 0 0 0
  hidden rotation_1 2.1039223514653317e-11 1 1.2182494836094435e-09 0.04859928083278234
  hidden linearVelocity_1 2.8140415357064978e-14 5.932170273589558e-15 2.0217195763153228e-16
  hidden angularVelocity_1 7.0942017928569675e-15 1.4182333057403477e-15 2.0042924409783273e-14
  hidden translation_2 0 0 0
  hidden rotation_2 -8.100845357857114e-12 1 4.294910798194088e-10 6.187250914176525
  hidden linearVelocity_2 -1.217372801852676e-14 6.365885607305998e-15 -9.06017233982243e-16
  hidden angularVelocity_2 7.137496247700306e-15 -1.948345116230854e-14 2.0107513958748865e-14
  hidden translation_3 0 0 0
  hidden rotation_3 0 -0.9999999999999999 0 5.51615039136937
  hidden linearVelocity_3 1.060651362134011e-13 -1.3158294407170042e-14 1.7870420109808105e-15
  hidden angularVelocity_3 7.328966139505e-14 2.026874623403351e-13 2.3321714466444248e-14
  hidden translation_4 0 0 0
  hidden rotation_4 0 -1 0 4.153781827069014
  hidden linearVelocity_4 1.872810547046242e-14 -1.979241609448711e-14 7.074676766530295e-16
  hidden angularVelocity_4 -5.215663454935202e-15 -6.685762932570582e-14 1.6812537675419262e-14
  translation -41.72345217571995 0.5865461326087824 0.23710600777717528
  rotation -0.0004859475899214589 -0.0033178239164271674 -0.9999943779338959 0.29096254145018197
  controllerArgs [
    "webots_chitu_gt.yaml"
  ]
}
DEF BOX0 Solid {
  translation -47.713253021240234 77.75593566894531 0
  rotation 0 0 1 -1.635180950164795
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 2 3 3
      }
    }
  ]
  name "box0"
  boundingObject Box {
    size 2 3 3
  }
}
DEF BOX1 Solid {
  translation -32.98416519165039 -3.5305283069610596 0
  rotation 0 0 1 -2.3113240388283887e-07
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 8 2 1
      }
    }
  ]
  name "box1"
  boundingObject Box {
    size 8 2 1
  }
}
DEF BOX2 Solid {
  translation -41.9292 -4.37428 0
  rotation 0 0 -1 3.11878
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 13 2 1
      }
    }
  ]
  name "box2"
  boundingObject Box {
    size 13 2 1
  }
}
DEF BOX7 Solid {
  translation 35.689918518066406 -53.66094970703125 0
  rotation 0 0 1 0.17932911217212677
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 5 3 3
      }
    }
  ]
  name "box7"
  boundingObject Box {
    size 5 3 3
  }
}
DEF BOX8 Solid {
  translation -61.38656997680664 72.07023620605469 0
  rotation 0 0 1 0.6690675616264343
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 5 3 3
      }
    }
  ]
  name "box8"
  boundingObject Box {
    size 5 3 3
  }
}
DEF BOX9 Solid {
  translation -42.489792 63.899055 0
  rotation 0 0 1 -3.103401
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 10 3 3
      }
    }
  ]
  name "box9"
  boundingObject Box {
    size 10 3 3
  }
}
DEF BOX10 Solid {
  translation -57.35160446166992 56.82969665527344 0
  rotation 0 0 1 0.004450690001249313
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 5 3 3
      }
    }
  ]
  name "box10"
  boundingObject Box {
    size 5 3 3
  }
}
DEF BOX4 Solid {
  translation -20.874882 -35.71714 0
  rotation 0 0 1 1.558527
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 5 3 3
      }
    }
  ]
  name "box1(1)"
  boundingObject Box {
    size 5 3 3
  }
}
DEF BOX11 Solid {
  translation -72.321358 73.462677 0
  rotation 0 0 1 0.845912
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 5 3 3
      }
    }
  ]
  name "box11"
  boundingObject Box {
    size 5 3 3
  }
}
DEF DBOX2 Robot {
  translation 13.640000000003056 16.479999999999563 1
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0 0 1
        }
      }
      geometry Box {
        size 0.2 0.2 0.2
      }
    }
  ]
  boundingObject Box {
    size 0.2 0.2 0.2
  }
  controller "path_motion"
  controllerArgs [
    "--type=line"
    "--radius=1.0"
    "--velocity=1.0"
    "--points=0.0,0.0;5.0,0.0;5.0,5.0;0.0,5.0"
  ]
  supervisor TRUE
  remoteControl ""
}
DEF BOX12 Solid {
  translation -42.2362 -10.8412 0
  rotation 0 0 1 -0.023505307179585877
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 8 2 1
      }
    }
  ]
  name "box12"
  boundingObject Box {
    size 8 2 1
  }
}
StraightRoadSegment {
}
DEF BOX13 Solid {
  translation -25.1666 -24.2918 0
  rotation 0 0 1 1.381234
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 1 0 0
        }
      }
      geometry Box {
        size 8 2 1
      }
    }
  ]
  name "BOX13"
  boundingObject Box {
    size 8 2 1
  }
}
DEF DBOX1 Robot {
  translation -11.534451402835465 -13.475999354656789 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0 0 1
        }
      }
      geometry Box {
        size 3 2 1
      }
    }
  ]
  name "DBOX1"
  boundingObject Box {
    size 3 2 1
  }
  controller "path_motion"
  controllerArgs [
    "--type=line"
    "--velocity=1.000000"
    "--points=-58.696636,-0.111811;-46.920380,-2.410176;-27.353434,-1.263004;-13.861792,-5.066780;-8.807552,-23.328917;-9.024250,-41.141582;-8.050838,-46.014030"
  ]
  supervisor TRUE
}
DEF DBOX3 Robot {
  translation -24.653359289148813 -0.6536080902856298 0
  children [
    Shape {
      appearance Appearance {
        material Material {
          diffuseColor 0 0 1
        }
      }
      geometry Box {
        size 3 2 1
      }
    }
  ]
  name "DBOX3"
  boundingObject Box {
    size 3 2 1
  }
  controller "path_motion"
  controllerArgs [
    "--type=line"
    "--velocity=1.000000"
    "--points=-24.752516,8.251123;-24.595537,-5.846317;-25.285852,-15.306780"
  ]
  supervisor TRUE
}
