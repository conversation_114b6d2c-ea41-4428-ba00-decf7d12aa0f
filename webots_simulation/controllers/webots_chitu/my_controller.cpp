// File:          my_controller.cpp
// Date:
// Description:
// Author:
// Modifications:

// You may need to add webots include files such as
// <webots/DistanceSensor.hpp>, <webots/Motor.hpp>, etc.
// and/or to add some other includes
#include <webots/Robot.hpp>

#include "webots_module.h"

// All the webots classes are defined in the "webots" namespace
using namespace webots;

// This is the main program of your controller.
// It creates an instance of your Robot instance, launches its
// function(s) and destroys it at the end of the execution.
// Note that only one instance of Robot should be created in
// a controller program.
// The arguments of the main function can be specified by the
// "controllerArgs" field of the Robot node
int main(int argc, char **argv)
{
    if (argc != 2) {
        printf("ERROR: please set controller args.\n");
        return -1;
    }

    std::string module_name("webots_mudule");
    std::string exec_name(argv[0]);
    std::string config_path(argv[1]);
    std::size_t found = exec_name.find_last_of("/");
    if (found) {
        std::string exec_base_path = exec_name.substr(0, found + 1);
        config_path = exec_base_path + config_path;
    }
    printf("INFO: config_path:%s\n", config_path.c_str());
    std::unique_ptr<uslam::module::WebotsModule> wb_dds_module = std::make_unique<uslam::module::WebotsModule>();

    wb_dds_module->init(config_path);

    while (wb_dds_module->step() != -1) {
        wb_dds_module->update();
    };

    return 0;
}
