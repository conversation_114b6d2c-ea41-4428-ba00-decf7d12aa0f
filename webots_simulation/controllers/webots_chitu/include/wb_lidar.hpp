#ifndef WB_LIDAR_H_
#define WB_LIDAR_H_

#include <webots/Lidar.hpp>

#include "sensor_msgs/msg/PointCloud2.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"

class DDSWbLidar : public DDSWbSensor
{
public:
    DDSWbLidar(webots::Robot *robot, int step, const UParam &param);
    bool onInit() override;
    void onPublish() override;
    bool isStepReady() override;

private:
    uslam::transport::WriterPtr<sensor_msgs::msg::PointCloud2> pc_writer_;
    webots::Lidar *lidar_;

    std::string wb_lidar_name_;
    std::string topic_name_, link_name_;
};
#endif