#ifndef WB_ULTRASONIC_H_
#define WB_ULTRASONIC_H_

#include <webots/DistanceSensor.hpp>

#include "sensor_msgs/msg/Range.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"
#include <webots/Robot.hpp>
#include "chitu_msgs/msg/ChituRangeArray.h"

class DDSWbUltrasonic : public DDSWbSensor
{
public:
    DDSWbUltrasonic(webots::Robot *robot, int step, const UParam &param);
    bool onInit() override;
    void onPublish() override;

private:
    uslam::transport::WriterPtr<sensor_msgs::msg::Range> pc_writer_;
    uslam::transport::WriterPtr<chitu_msgs::msg::ChituRangeArray> ultrasonics_writer_;
    webots::DistanceSensor *ultrasonic_;
    std::vector<webots::DistanceSensor *> ultrasonic_sensors_;

    std::string wb_ultrasonic_name_;
    std::string topic_name_, link_name_;
};
#endif
