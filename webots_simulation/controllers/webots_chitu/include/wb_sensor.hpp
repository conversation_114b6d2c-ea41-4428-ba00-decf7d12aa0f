#ifndef WB_SENSOR_H_
#define WB_SENSOR_H_

#include <uparam/uparam.h>

#include <string>
#include <webots/Device.hpp>
#include <webots/Robot.hpp>

class DDSWbSensor
{
public:
    // DDSWbSensor(webots::Robot *robot, std::string name, int step, std::string topic_name, std::string link_name);
    DDSWbSensor(std::string name, webots::Robot *robot, int step, const UParam &param);

    bool init();
    void publishValue();
    virtual bool isStepReady();

    virtual bool onInit() = 0;
    virtual void onPublish() = 0;

protected:
    std::string name_;
    webots::Robot *const robot_;
    const int sensor_step_;
    const UParam param_;

    double last_time_ = 0;
    bool inited_ = false;
};
#endif