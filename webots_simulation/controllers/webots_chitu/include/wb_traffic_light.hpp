#ifndef WB_TL_H_
#define WB_TL_H_
#include <webots/Supervisor.hpp>

#include "custom_msgs/msg/PerceptionObjects.h"
#include "sensor_msgs/msg/Imu.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"

class DDSWbTrafficLight : public DDSWbSensor
{
public:
    DDSWbTrafficLight(webots::Robot *robot, int step, const UParam &param);
    ~DDSWbTrafficLight();

    bool onInit() override;
    void onPublish() override;

private:
    webots::Supervisor *super_robot_;
    std::string base_robot_def_, target_def_prefix_;
    std::string topic_name_, link_name_;

    uslam::transport::WriterPtr<custom_msgs::msg::PerceptionObjects> object_writer_;
    std::map<int, std::pair<std::string, webots::Node *>> node_list_;
    webots::Node *main_robot_node_;

    double x_min_, x_max_, y_min_, y_max_;

    void updateNode();
    int update_cnt = 0;
};
#endif