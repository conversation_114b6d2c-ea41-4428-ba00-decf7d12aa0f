#ifndef WB_IMU_H_
#define WB_IMU_H_

#include <webots/Accelerometer.hpp>
#include <webots/Gyro.hpp>
#include <webots/InertialUnit.hpp>

#include "sensor_msgs/msg/Imu.h"
#include "utransport/writer.h"
#include "wb_sensor.hpp"

class DDSWbImu : public DDSWbSensor
{
public:
    DDSWbImu(webots::Robot *robot, int step, const UParam &param);
    bool onInit() override;
    void onPublish() override;

private:
    uslam::transport::WriterPtr<sensor_msgs::msg::Imu> writer_;
    webots::Accelerometer *accelerometer_;
    webots::Gyro *gyro_;
    webots::InertialUnit *iu_;

    std::string wb_acc_name_, wb_gyro_name_, wb_iu_name_;
    std::string topic_name_, link_name_;
};
#endif