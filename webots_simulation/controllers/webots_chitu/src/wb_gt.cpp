#include "wb_gt.hpp"

#include "utf/tf2/utils.h"
#include <pcl/features/moment_of_inertia_estimation.h>
#include <opencv2/core/types.hpp>
#include <opencv2/opencv.hpp>
#include <pcl/impl/point_types.hpp>
#include "json/reader.h"
#include "utf/transform_broadcaster.h"
#include "utf/utf.h"

DDSWbGt::DDSWbGt(webots::Robot *robot, int step, const UParam &param) : DDSWbSensor("gt",robot, step, param)
{
    super_robot_ = (webots::Supervisor *)robot;
}

DDSWbGt::~DDSWbGt()
{
    if (super_robot_) delete super_robot_;
}

bool DDSWbGt::onInit()
{
    std::string car_ground_truth;

    param_.param("base_robot_def", base_robot_def_, std::string(""));
    param_.param("target_def_prefix_list", target_def_prefix_list_, std::vector<std::string>());
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));
    param_.param("x_min", x_min_, -50.0);
    param_.param("x_max", x_max_, 50.0);
    param_.param("y_min", y_min_, -50.0);
    param_.param("y_max", y_max_, 50.0);
    param_.param("skip_cnt", skip_cnt_, 0);
    param_.param("car_ground_truth", car_ground_truth, std::string(""));
    param_.param<bool>("use_ground_truth_tf", use_ground_truth_tf_, false);

    printf(
        "DDSWbGt::onInit topic_name:%s, base_robot_def_:%s, car_ground_truth:%s, use_ground_truth_tf:%d, x_min: %f, "
        "x_max: %f, y_min: %f, "
        "y_max: %f\n",
        topic_name_.c_str(), base_robot_def_.c_str(), car_ground_truth.c_str(), use_ground_truth_tf_, x_min_, x_max_,
        y_min_, y_max_);
    car_ground_truth_writer_ =
        std::make_shared<uslam::transport::Writer<geometry_msgs::msg::PoseStamped>>(car_ground_truth);
    if (car_ground_truth_writer_) {
        ground_truth_localization_pose_writer_ =
            std::make_shared<uslam::transport::Writer<nav_msgs::msg::Odometry>>("/localization_pose_base");
    }
    if (use_ground_truth_tf_) {
        init_pose_reader_ = std::make_shared<uslam::transport::Reader<geometry_msgs::msg::PoseWithCovarianceStamped>>(
            "/uslam/hmi/initial_pose", std::bind(&DDSWbGt::initPoseCallback, this, std::placeholders::_1));
    }

    object_writer_ = std::make_shared<uslam::transport::Writer<custom_msgs::msg::PerceptionObjects>>(topic_name_);
    all_object_writer_ =
        std::make_shared<uslam::transport::Writer<custom_msgs::msg::PerceptionObjects>>("/all_webots_objects");

    set_object_reader_ = std::make_shared<uslam::transport::Reader<webots_objects_msgs::msg::WbObjectList>>(
        "set_objects_topic", std::bind(&DDSWbGt::setObjectCallback, this, std::placeholders::_1));

    get_service_ =
        std::make_shared<uslam::RosService<webots_objects_msgs::srv::Get_Request, webots_objects_msgs::srv::Get_Response>>(
            "get_objects", std::bind(&DDSWbGt::getServiceCallback, this, std::placeholders::_1, std::placeholders::_2));
    get_service_->Init();

    set_service_ =
        std::make_shared<uslam::RosService<webots_objects_msgs::srv::Set_Request, webots_objects_msgs::srv::Set_Response>>(
            "set_objects", std::bind(&DDSWbGt::setServiceCallback, this, std::placeholders::_1, std::placeholders::_2));
    set_service_->Init();

    main_robot_node_ = super_robot_->getFromDef(base_robot_def_);
    if (!main_robot_node_) {
        ULOGW("DDSWbGt::onInit main_robot_node_ is null");
        return false;
    }
    object_process_ = std::make_unique<ObjectProcess>(super_robot_, main_robot_node_);

    return true;
}

void DDSWbGt::pubCarGroundTruth()
{
    auto pose = object_process_->getRobotPose();

    geometry_msgs::msg::PoseStamped car_ground_truth_msg;
    car_ground_truth_msg.header.frame_id = "world";
    unav::Time current_time;
    current_time.fromSec(super_robot_->getTime());
    car_ground_truth_msg.header.stamp = current_time;
    car_ground_truth_msg.pose = pose;
    car_ground_truth_writer_->Write(car_ground_truth_msg);

    if (use_ground_truth_tf_) {
        utf::TransformBroadcaster tfb("map");
        geometry_msgs::msg::TransformStamped transform;
        transform.header.frame_id = "map";
        transform.header.stamp = car_ground_truth_msg.header.stamp;
        transform.child_frame_id = "base_link";
        transform.transform.translation.x = car_ground_truth_msg.pose.position.x;
        transform.transform.translation.y = car_ground_truth_msg.pose.position.y;
        transform.transform.translation.z = car_ground_truth_msg.pose.position.z;
        transform.transform.rotation.x = car_ground_truth_msg.pose.orientation.x;
        transform.transform.rotation.y = car_ground_truth_msg.pose.orientation.y;
        transform.transform.rotation.z = car_ground_truth_msg.pose.orientation.z;
        transform.transform.rotation.w = car_ground_truth_msg.pose.orientation.w;
        tfb.sendTransform(transform);

        nav_msgs::msg::Odometry localization_psoe_msg;
        localization_psoe_msg.header.frame_id = "map";
        localization_psoe_msg.header.stamp = car_ground_truth_msg.header.stamp;
        localization_psoe_msg.child_frame_id = "base_link";
        localization_psoe_msg.pose.pose = car_ground_truth_msg.pose;
        ground_truth_localization_pose_writer_->Write(localization_psoe_msg);
    }
}
void DDSWbGt::onPublish()
{
    // printf("\033[1;33m Publish once update_cnt=%d\033[0m\n", update_cnt);
    pubCarGroundTruth();

    object_process_->updateAllObjectsPose();

    update_cnt++;
    if (update_cnt > 100) {
        object_process_->updateUnknownNode();
        publishObjects(true);
        update_cnt = 0;
    }

    if (skip_cnt_ != 0) {
        if (update_cnt % (skip_cnt_ + 1) != 0) return;
    }

    publishObjects(false);
}

void DDSWbGt::initPoseCallback(const std::shared_ptr<geometry_msgs::msg::PoseWithCovarianceStamped> &msg)
{
    object_process_->setRobotPose(msg->pose.pose.position.x, msg->pose.pose.position.y,
                                  tf2::getYaw(msg->pose.pose.orientation));
}

void DDSWbGt::publishObjects(bool publish_all)
{
    custom_msgs::msg::PerceptionObjects objects_msg;
    objects_msg.header.frame_id = link_name_;
    unav::Time current_time;
    current_time.fromSec(super_robot_->getTime());
    objects_msg.header.stamp = current_time;
    if (publish_all) {
        object_process_->getPerceptionObjects(target_def_prefix_list_, objects_msg);
        all_object_writer_->Write(objects_msg);
    } else {
        object_process_->getPerceptionObjects(target_def_prefix_list_, objects_msg, x_min_, x_max_, y_min_, y_max_);
        object_writer_->Write(objects_msg);
    }
}

void DDSWbGt::setServiceCallback(const webots_objects_msgs::srv::Set_Request::ConstPtr &req,
                                 webots_objects_msgs::srv::Set_Response::Ptr &res)
{
    for (const auto &item : req->objects.objects) {
        ULOGI("DDSWbGt::setServiceCallback %d type:%d frame_id:%s", item.action, item.type,
              req->objects.header.frame_id.c_str());
        if (item.action == webots_objects_msgs::msg::WbObject_Constants::DELETEALL) {
            object_process_->deleteAllObjects();
        } else if (item.action == webots_objects_msgs::msg::WbObject_Constants::ADD) {
            if (req->objects.header.frame_id == "base_link") {
                object_process_->setObstacle(item, true);
            } else {
                object_process_->setObstacle(item);
            }
        }
    }
}

void DDSWbGt::setObjectCallback(const std::shared_ptr<webots_objects_msgs::msg::WbObjectList> &msg)
{
    for (const auto &item : msg->objects) {
        ULOGI("DDSWbGt::setObjectCallback %d type:%d frame_id:%s", item.action, item.type, msg->header.frame_id.c_str());
        if (item.action == webots_objects_msgs::msg::WbObject_Constants::DELETEALL) {
            object_process_->deleteAllObjects();
        } else if (item.action == webots_objects_msgs::msg::WbObject_Constants::ADD) {
            if (msg->header.frame_id == "base_link") {
                object_process_->setObstacle(item, true);
            } else {
                object_process_->setObstacle(item);
            }
        }
    }
}

void DDSWbGt::getServiceCallback(const webots_objects_msgs::srv::Get_Request::ConstPtr &req,
                                 webots_objects_msgs::srv::Get_Response::Ptr &res)
{
    ULOGI("DDSWbGt::getServiceCallback type:%d", req->type);
    res->objects.objects.clear();
    if (req->type == webots_objects_msgs::srv::Get_Request_Constants::ALL) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::STATIC, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON, res->objects);
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::STATIC) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::STATIC, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_CIRCLE) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_CIRCLE, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_POLYGON) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_POLYGON, res->objects);
    } else if (req->type == webots_objects_msgs::srv::Get_Request_Constants::DYNAMIC_LINES) {
        object_process_->getObstacle(webots_objects_msgs::msg::WbObject_Constants::DYNAMIC_LINES, res->objects);
    }

    res->success = true;
}
