#include "wb_car.hpp"

#include "utf/transform_broadcaster.h"
#include "utf/utf.h"

DDSWbCar::DDSWbCar(webots::Car *car_driver, int step, const UParam &param) :
    DDSWbSensor("car",car_driver, step, param), car_driver_(car_driver)
{
}

bool DDSWbCar::onInit()
{
    param_.param("odom_out_topic", odom_out_topic_, std::string(""));
    param_.param("ackermann_odom_out_topic", ackermann_odom_out_topic_, std::string(""));
    param_.param("twist_in_topic", twist_in_topic_, std::string(""));
    param_.param("ackermann_twist_in_topic", ackermann_twist_in_topic_, std::string(""));

    printf("DDSWbCar::Init odom_out:%s ackermann_odom_out:%s twist_in:%s ackermann_twist_in:%s\n", odom_out_topic_.c_str(),
           ackermann_odom_out_topic_.c_str(), twist_in_topic_.c_str(), ackermann_twist_in_topic_.c_str());
    twist_reader_ = std::make_shared<uslam::transport::Reader<geometry_msgs::msg::Twist>>(
        twist_in_topic_, std::bind(&DDSWbCar::twistCallback, this, std::placeholders::_1));

    ackermann_twist_reader_ = std::make_shared<uslam::transport::Reader<ackermann_msgs::msg::AckermannDriveStamped>>(
        ackermann_twist_in_topic_, std::bind(&DDSWbCar::ackermannTwistCallback, this, std::placeholders::_1));

    uslam::transport::TransportAttribute engage_attr(
        "/uslam/hmi/engaged", uslam::transport::DDSQosProfile::QOS_PROFILE_RELIABLE_LAST_TRANSIENT());
    engage_reader_ = std::make_shared<uslam::transport::Reader<std_msgs::msg::Bool>>(
        engage_attr, std::bind(&DDSWbCar::EngageCallback, this, std::placeholders::_1));

    odom_writer_ = std::make_shared<uslam::transport::Writer<nav_msgs::msg::Odometry>>(odom_out_topic_);
    ackermann_odom_writer_ =
        std::make_shared<uslam::transport::Writer<ackermann_msgs::msg::AckermannDriveOdometry>>(ackermann_odom_out_topic_);
    return true;
}

void DDSWbCar::EngageCallback(const std::shared_ptr<std_msgs::msg::Bool> &msg)
{
    ULOGI("engage callback: %d ", msg.get()->data);
    std::unique_lock<std::mutex> lock(engage_mutex_);
    engage_ = msg.get()->data;
}
void DDSWbCar::twistCallback(const std::shared_ptr<geometry_msgs::msg::Twist> &msg)
{
    car_driver_->setCruisingSpeed(msg->linear.x * 3.6);
    car_driver_->setSteeringAngle(-msg->angular.z);
}
void DDSWbCar::ackermannTwistCallback(const std::shared_ptr<ackermann_msgs::msg::AckermannDriveStamped> &msg)
{
    std::unique_lock<std::mutex> lock(engage_mutex_);
    if (!engage_) {
        car_driver_->setCruisingSpeed(0.0);
        car_driver_->setSteeringAngle(0.0);
        return;
    }
    lock.unlock();
    if (msg->drive.steering_angle > 0.55)
        msg->drive.steering_angle = 0.55;
    else if (msg->drive.steering_angle < -0.55)
        msg->drive.steering_angle = -0.55;

    car_driver_->setCruisingSpeed(msg->drive.speed * 3.6);
    car_driver_->setSteeringAngle(-msg->drive.steering_angle);
}
void DDSWbCar::onPublish()
{
    double realtime_steering_angle = -car_driver_->getSteeringAngle();
    double realtime_veh_speed = car_driver_->getCurrentSpeed() / 3.6;
    if (std::isnan(realtime_veh_speed) || std::isnan(realtime_steering_angle)) return;

    // 解算位姿里程计odom
    if (last_odom_time_ < 0) {
        last_odom_time_ = car_driver_->getTime();
        return;
    }

    double delta_t = car_driver_->getTime() - last_odom_time_;
    double radius = car_driver_->getWheelbase() * tan(M_PI_2 - realtime_steering_angle);
    double delta_odom = realtime_veh_speed * delta_t;
    double delta_alpha = delta_odom / radius;
    double delta_y = radius - cos(delta_alpha) * radius;
    double delta_x = sin(delta_alpha) * radius;

    odom_x_ += cos(odom_theta_) * delta_x - sin(odom_theta_) * delta_y;
    odom_y_ += sin(odom_theta_) * delta_x + cos(odom_theta_) * delta_y;
    odom_theta_ += delta_alpha;
    odom_theta_ = atan2(sin(odom_theta_), cos(odom_theta_));

    nav_msgs::msg::Odometry odom_msg;
    unav::Time current_time;
    current_time.fromSec(car_driver_->getTime());
    odom_msg.header.frame_id = "odom";
    odom_msg.header.stamp = current_time;
    odom_msg.child_frame_id = "base_link";
    odom_msg.pose.pose.position.x = odom_x_;
    odom_msg.pose.pose.position.y = odom_y_;
    odom_msg.pose.pose.orientation = tf2::createQuaternionMsgFromYaw(odom_theta_);
    // double angular_z = realtime_veh_speed / realtime_center_y;
    // double linear_x = sin(angular_z) * realtime_center_y;
    // double linear_y = (1 - cos(angular_z)) * realtime_center_y;
    odom_msg.twist.twist.linear.x = realtime_veh_speed;
    // odom_msg.twist.twist.linear.y = linear_y;
    // odom_msg.twist.twist.angular.z = angular_z;
    odom_writer_->Write(odom_msg);

    ackermann_msgs::msg::AckermannDriveOdometry ackermann_odom_msg;
    ackermann_odom_msg.header.frame_id = "base_link";
    ackermann_odom_msg.header.stamp = current_time;
    ackermann_odom_msg.steering_angle = realtime_steering_angle;
    ackermann_odom_msg.speed = realtime_veh_speed;
    ackermann_odom_writer_->Write(ackermann_odom_msg);

    // utf::TransformBroadcaster tfb("odom");
    // geometry_msgs::msg::TransformStamped transform;
    // transform.header.frame_id = "odom";
    // transform.header.stamp = current_time;
    // transform.child_frame_id = "base_link";
    // transform.transform.translation.x = odom_x_;
    // transform.transform.translation.y = odom_y_;
    // transform.transform.translation.z = 0;
    // transform.transform.rotation = odom_msg.pose.pose.orientation;
    // tfb.sendTransform(transform);

    last_odom_time_ = car_driver_->getTime();
}
