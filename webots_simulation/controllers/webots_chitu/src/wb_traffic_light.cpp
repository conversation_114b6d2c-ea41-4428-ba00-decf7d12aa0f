#include "wb_traffic_light.hpp"

#include <Eigen/Core>
#include <Eigen/Geometry>

DDSWbTrafficLight::DDSWbTrafficLight(webots::Robot *robot, int step, const UParam &param) :
    DDSWbSensor("traffic_light",robot, step, param)
{
    super_robot_ = (webots::Supervisor *)robot;
}
DDSWbTrafficLight::~DDSWbTrafficLight()
{
    if (super_robot_) delete super_robot_;
}

bool DDSWbTrafficLight::onInit()
{
    param_.param("base_robot_def", base_robot_def_, std::string(""));
    param_.param("target_def_prefix", target_def_prefix_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));

    object_writer_ = std::make_shared<uslam::transport::Writer<custom_msgs::msg::PerceptionObjects>>(topic_name_);

    main_robot_node_ = super_robot_->getFromDef(base_robot_def_);
    if (!main_robot_node_) return false;
    updateNode();

    x_min_ = 0;
    x_max_ = 50.0;
    y_min_ = -50.0;
    y_max_ = 50.0;
    return true;
}

void DDSWbTrafficLight::updateNode()
{
    for (int i = 0; i < 100; i++) {
        std::string name = target_def_prefix_ + std::to_string(i);
        auto node_ptr = super_robot_->getFromDef(name);
        if (node_ptr) {
            printf("Find %s\n", name.c_str());
            if (node_list_.find(i) == node_list_.end()) node_list_.insert({i, std::make_pair(name, node_ptr)});
        }
    }
}

void DDSWbTrafficLight::onPublish()
{
    // update_cnt++;
    // if (update_cnt > 100) {
    //     updateNode();
    //     update_cnt = 0;
    // }

    // custom_msgs::msg::PerceptionObjects objects_msg;
    // objects_msg.header.frame_id = link_name_;
    // unav::Time current_time;
    // current_time.fromSec(super_robot_->getTime());
    // objects_msg.header.stamp = current_time;

    for (auto it = node_list_.begin(); it != node_list_.end(); it++) {
        auto node_ptr = it->second.second;
        const double *pose = node_ptr->getPose(main_robot_node_);
        Eigen::Matrix4f matrix;
        matrix << pose[0], pose[1], pose[2], pose[3], pose[4], pose[5], pose[6], pose[7], pose[8], pose[9], pose[10],
            pose[11], pose[12], pose[13], pose[14], pose[15];
        Eigen::Quaternionf quaternion(matrix.block<3, 3>(0, 0));
        Eigen::Vector3f position(matrix.block<3, 1>(0, 3));
        auto field = node_ptr->getField("slot");
        printf("name:%s pose:%f %f %f field:%s type:%d cvt:%d\n", it->second.first.c_str(), position[0], position[1],
               position[2], field->getName().c_str(), field->getType(), field->getCount());
        if (field->getType() == webots::Field::MF_NODE) {
            for (int i = 0; i < field->getCount(); i++) {
                auto field_node = field->getMFNode(i);
                if (field_node->getTypeName() == "TrafficLight") {
                    auto field_color = field_node->getField("recognitionColors");
                    if (field_color->getType() == webots::Field::MF_COLOR && field_color->getCount() == 2) {
                        auto color = field_color->getMFColor(1);
                        std::string color_str;
                        if (color[0] == 1.0 && color[1] == 0.0 && color[2] == 0.0)
                            color_str = "red";
                        else if (color[0] == 0.0 && color[1] == 1.0 && color[2] == 0.0)
                            color_str = "green";
                        else if (color[0] == 1.0 && color[1] == 0.5 && color[2] == 0.0)
                            color_str = "yellow";
                        else
                            color_str = "unknown";
                        printf("i:%d def:%s type:%s field_color:%s\n", i, field_node->getDef().c_str(),
                               field_node->getTypeName().c_str(), color_str.c_str());
                    }
                }
            }
        }

        //     if (position[0] < x_min_ || position[0] > x_max_ || position[1] < y_min_ || position[1] > y_max_)
        //     continue; custom_msgs::msg::PerceptionObject object_msg; object_msg.pose.position.x = position[0];
        //     object_msg.pose.position.y = position[1]; object_msg.pose.position.z = position[2];
        //     object_msg.pose.orientation.x = quaternion.x(); object_msg.pose.orientation.y = quaternion.y(); object_msg.pose.orientation.z
        //     = quaternion.z(); object_msg.pose.orientation.w = quaternion.w(); object_msg.id = it->first; object_msg.size.x
        //     = 3.5; object_msg.size.y = 1.7; object_msg.size.z = 1.0; object_msg.oc.classification =
        //     custom_msgs::msg::ObjectClassification_Constants::CAR; objects_msg.objects.push_back(object_msg);
    }
    // object_writer_->Write(objects_msg);
}