#include "wb_camera.hpp"

using namespace webots;

std::shared_ptr<sensor_msgs::msg::Image> cvMatToSensorMsgsImage(std_msgs::msg::Header &header, const cv::Mat &mat)
{
    std::shared_ptr<sensor_msgs::msg::Image> image_ptr = std::make_shared<sensor_msgs::msg::Image>();
    image_ptr->width = mat.cols;
    image_ptr->height = mat.rows;
    image_ptr->encoding = "bgr8";
    image_ptr->step = mat.cols * mat.elemSize();
    image_ptr->data.resize(image_ptr->step * mat.rows);

    if (mat.isContinuous()) {
        memcpy(reinterpret_cast<char *>(&image_ptr->data[0]), mat.data, image_ptr->step * mat.rows);
    } else {
        uchar *c_ptr = reinterpret_cast<uchar *>(&image_ptr->data[0]);
        uchar *cv_ptr = mat.data;
        for (unsigned int i = 0; i < mat.rows; ++i) {
            memcpy(c_ptr, cv_ptr, image_ptr->step);
            c_ptr += image_ptr->step;
            cv_ptr += mat.step;
        }
    }

    image_ptr->header = header;

    return image_ptr;
}

DDSWbCamera::DDSWbCamera(webots::Robot *robot, int step, const UParam &param) :
    DDSWbSensor("camera", robot, step, param)
{
}

bool DDSWbCamera::onInit()
{
    param_.param("wb_camera_name", wb_camera_name_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("road_line_topic_name", road_line_topic_name_, std::string(""));
    param_.param("drivable_space_topic_name", drivable_space_topic_name_, std::string(""));
    param_.param("drivable_space_image_topic_name", drivable_space_image_topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));

    param_.param<bool>("obtain_drivable_space_msg", obtain_drivable_space_msg_, false);
    param_.param<bool>("obtain_road_line_msg", obtain_road_line_msg_, false);

    camera_ = (webots::Camera *)robot_->getDevice(wb_camera_name_);

    if (camera_ == nullptr) return false;
    camera_->enable(sensor_step_);
    if (camera_->hasRecognition()) {
        camera_->recognitionEnable(sensor_step_);
        camera_->enableRecognitionSegmentation();
    }
    image_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::Image>>(topic_name_);
    road_line_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::Image>>(road_line_topic_name_);
    drivable_space_writer_ =
        std::make_shared<uslam::transport::Writer<drivable_space_msgs::msg::DrivableSpace>>(drivable_space_topic_name_);
    drivable_space_image_writer_ =
        std::make_shared<uslam::transport::Writer<sensor_msgs::msg::Image>>(drivable_space_image_topic_name_);
    return true;
}

cv::Mat DDSWbCamera::GetRoadLineImage(const webots::CameraRecognitionObject *head_object_ptr, int object_num,
                                      const cv::Mat &camera_image)
{
    cv::Mat road_line_image = camera_image.clone();

    auto pass_contours = GetRoadLineContours(head_object_ptr, object_num, road_line_image);

    road_line_image.setTo(cv::Scalar(0, 0, 0));
    for (int i = 0; i < pass_contours.size(); ++i) {
        cv::drawContours(road_line_image, pass_contours, static_cast<int>(i), cv::Scalar(255, 255, 255), cv::FILLED);
    }
    cv::resize(road_line_image, road_line_image, cv::Size(1024, 512));
    // cv::drawContours(road_line_image, pass_contours, -1, cv::Scalar(0, 255, 0), 2);

    return road_line_image;
}

std::vector<std::vector<cv::Point>> DDSWbCamera::GetRoadLineContours(const webots::CameraRecognitionObject *head_object_ptr,
                                                                     int object_num, const cv::Mat &camera_image)
{
    cv::Mat road_line_image = camera_image.clone();

    std::vector<std::vector<cv::Point>> no_road_line_points;
    for (int i = 0; i < object_num; ++i) {
        auto ptr = (head_object_ptr + i);
        auto object_id = ptr->id;
        auto object_model = ptr->model;
        std::cout << "object_id: " << object_id << "model: " << object_model << std::endl;
        if (strcmp(object_model, std::string("speed limit panel").c_str()) == 0 ||
            strcmp(object_model, std::string("advertising board").c_str()) == 0) {
            std::vector<cv::Point> corner;
            cv::Point letf_top(ptr->position_on_image[0] - ptr->size_on_image[0] / 2,
                               ptr->position_on_image[1] - ptr->size_on_image[1] / 2);
            corner.push_back(letf_top);
            cv::Point right_down(ptr->position_on_image[0] + ptr->size_on_image[0] / 2,
                                 ptr->position_on_image[1] + ptr->size_on_image[1] / 2);
            corner.push_back(right_down);
            no_road_line_points.push_back(corner);
        }
    }

    for (const auto point : no_road_line_points) {
        cv::rectangle(road_line_image, point[0], point[1], cv::Scalar(255, 0, 0), 2);
    }
    cv::Mat lane_mask;

    cv::Scalar lower_bound = cv::Scalar(255, 255, 255);
    cv::Scalar upper_bound = cv::Scalar(255, 255, 255);
    cv::inRange(road_line_image, lower_bound, upper_bound, lane_mask);
    // 形态学处理以去除噪声
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(2, 2));
    cv::morphologyEx(lane_mask, lane_mask, cv::MORPH_OPEN, kernel);

    // 查找轮廓
    std::vector<std::vector<cv::Point>> contours, pass_contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(lane_mask, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE);
    std::cout << "contours size: " << contours.size() << std::endl;
    // 在原始图像上绘制轮廓
    for (const auto &contour : contours) {
        bool pass = true;
        for (const auto &point : contour) {
            for (const auto &cornor : no_road_line_points) {
                cv::Rect rect(cornor[0], cornor[1]);
                if (rect.contains(point)) {
                    pass = false;
                    break;
                }
            }
        }
        if (!pass) continue;
        pass_contours.push_back(contour);
    }
    return pass_contours;
}

cv::Mat DDSWbCamera::GetDrivableSpaceImage(const webots::CameraRecognitionObject *head_object_ptr, int object_num,
                                           const cv::Mat &camera_image)
{
    cv::Mat drivable_space_image = camera_image.clone();

    cv::Mat drivable_space_mask;
    // cv::Scalar lower_bound = cv::Scalar(0.96 * 255, 255 * 0.82, 0.05 * 255);
    // cv::Scalar upper_bound = cv::Scalar(0.96 * 255, 255 * 0.82, 0.05 * 255);
    cv::Scalar lower_bound = cv::Scalar(51, 51, 51);
    cv::Scalar upper_bound = cv::Scalar(51, 51, 51);
    cv::inRange(drivable_space_image, lower_bound, upper_bound, drivable_space_mask);
    // 形态学处理以去除噪声
    cv::Mat kernel = cv::getStructuringElement(cv::MORPH_RECT, cv::Size(2, 2));
    cv::morphologyEx(drivable_space_mask, drivable_space_mask, cv::MORPH_OPEN, kernel);

    // 查找轮廓
    std::vector<std::vector<cv::Point>> contours;
    std::vector<cv::Vec4i> hierarchy;
    cv::findContours(drivable_space_mask, contours, hierarchy, cv::RETR_EXTERNAL, cv::CHAIN_APPROX_NONE);
    drivable_space_image = cv::Mat::zeros(camera_image.size(), CV_8UC3);
    // cv::drawContours(drivable_space_image, contours, -1, cv::Scalar(0, 255, 0), 2);
    for (size_t i = 0; i < contours.size(); ++i) {
        cv::drawContours(drivable_space_image, contours, static_cast<int>(i), cv::Scalar(255, 255, 255), cv::FILLED);
    }

    auto road_line_contours = GetRoadLineContours(head_object_ptr, object_num, camera_image);
    for (size_t i = 0; i < road_line_contours.size(); ++i) {
        cv::drawContours(drivable_space_image, road_line_contours, static_cast<int>(i), cv::Scalar(255, 255, 255),
                         cv::FILLED);
    }

    std::vector<std::vector<cv::Point>> no_road_line_points;
    for (int i = 0; i < object_num; ++i) {
        auto ptr = (head_object_ptr + i);
        auto object_id = ptr->id;
        auto object_model = ptr->model;
        if (strcmp(object_model, std::string("pedestrian crossing").c_str()) == 0) {
            std::cout << "pedestrian crossing color number: " << ptr->number_of_colors << std::endl;
            for (int j = 0; j < ptr->number_of_colors; j++) {
                std::cout << "pedestrian crossing color: %f " << *(ptr->colors + j) << std::endl;
            }
        }
    }

    return drivable_space_image;
}

void DDSWbCamera::onPublish()
{
    image_count_++;
    while (image_count_ >= 10) {
        image_count_ = 0;
        // const unsigned char *image = camera_->getImage();
        const unsigned char *image = camera_->getRecognitionSegmentationImage();
        auto head_object_ptr = camera_->getRecognitionObjects();
        auto object_num = camera_->getRecognitionNumberOfObjects();
        cv::Mat cvimage(camera_->getHeight(), camera_->getWidth(), CV_8UC4, const_cast<unsigned char *>(image));
        cv::cvtColor(cvimage, cvimage, cv::COLOR_BGRA2BGR);

        // 可行驶区域
        if (obtain_drivable_space_msg_) {
            auto drivable_space_result = GetDrivableSpaceImage(head_object_ptr, object_num, cvimage);
            std_msgs::msg::Header header;
            unav::Time timestamp;
            timestamp.fromSec(robot_->getTime());
            header.stamp = timestamp;
            header.frame_id = link_name_;
            auto image_ptr = cvMatToSensorMsgsImage(header, drivable_space_result);
            drivable_space_image_writer_->Write(image_ptr);
            drivable_space_msgs::msg::DrivableSpace drivable_space_msg;
            drivable_space_msg.header = header;
            drivable_space_msg.image = *image_ptr;
            drivable_space_writer_->Write(drivable_space_msg);
        }
        // 车道线
        if (obtain_road_line_msg_) {
            auto road_line_result = GetRoadLineImage(head_object_ptr, object_num, cvimage);
            std_msgs::msg::Header header;
            unav::Time timestamp;
            timestamp.fromSec(robot_->getTime());
            header.stamp = timestamp;
            header.frame_id = link_name_;
            auto image_ptr = cvMatToSensorMsgsImage(header, road_line_result);
            road_line_writer_->Write(image_ptr);
        }

        std_msgs::msg::Header header;
        unav::Time timestamp;
        timestamp.fromSec(robot_->getTime());
        header.stamp = timestamp;
        header.frame_id = link_name_;
        auto image_ptr = cvMatToSensorMsgsImage(header, cvimage);
        image_writer_->Write(image_ptr);
    }
}
