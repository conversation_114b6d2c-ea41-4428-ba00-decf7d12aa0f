#include "wb_ultrasonic.hpp"

#include "pcl/point_types.h"
#include "pcl_conversions/pcl_conversions.h"
DDSWbUltrasonic::DDSWbUltrasonic(webots::Robot *robot, int step, const UParam &param) :
    DDSWbSensor("ultrasonic", robot, step, param)
{
}

bool DDSWbUltrasonic::onInit()
{
    param_.param("wb_ultrasonic_name", wb_ultrasonic_name_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));
    std::vector<std::string> sensorNames = {"ultrasonic0", "ultrasonic1", "ultrasonic2",  "ultrasonic3",
                                            "ultrasonic4", "ultrasonic5", "ultrasonic6",  "ultrasonic7",
                                            "ultrasonic8", "ultrasonic9", "ultrasonic10", "ultrasonic11"};
    // ultrasonic_ = (webots::DistanceSensor *)robot_->getDistanceSensor("ultrasonic5");

    for (const auto &name : sensorNames) {
        webots::DistanceSensor *sensor = robot_->getDistanceSensor(name.c_str());
        if (sensor) {
            ultrasonic_sensors_.push_back(sensor);

        } else {
            std::cerr << "Failed to get sensor: " << name << std::endl;
            return false;
        }
    }
    for (auto sensor : ultrasonic_sensors_) {
        sensor->enable(sensor_step_);
        printf("DDSWbultrasonic::init wb_ultrasonic_name:%s step:%d topic_name:%s link_name:%s\n",
               wb_ultrasonic_name_.c_str(), sensor_step_, topic_name_.c_str(), sensor->getName().c_str());
    }
    // if (ultrasonic_ == nullptr) return false;

    // ultrasonic_->enablePointCloud();

    // pc_writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::Range>>(topic_name_);

    ultrasonics_writer_ = std::make_shared<uslam::transport::Writer<chitu_msgs::msg::ChituRangeArray>>(topic_name_);

    return true;
}
void DDSWbUltrasonic::onPublish()
{
    chitu_msgs::msg::ChituRangeArray ultrasonic_msg;
    unav::Time timestamp;
    timestamp.fromSec(robot_->getTime());
    ultrasonic_msg.header.stamp = timestamp;
    ultrasonic_msg.header.frame_id = "base_link";
    for (auto sensor : ultrasonic_sensors_) {
        unav::Time timestamp;
        timestamp.fromSec(robot_->getTime());
        sensor_msgs::msg::Range range_msg;
        range_msg.header.stamp = timestamp;
        range_msg.header.frame_id = sensor->getName();  // 设定消息的frame_id
        // range_msg.radiation_type = sensor_msgs::msg::Range::radiation_type();
        range_msg.field_of_view = sensor->getAperture();
        range_msg.min_range = sensor->getMinValue();
        range_msg.max_range = sensor->getMaxValue();
        double dis = sensor->getValue();
        if (dis > 39) {
            range_msg.range = 0;
        } else {
            range_msg.range = dis;
        }
        ultrasonic_msg.names.push_back(sensor->getName());
        ultrasonic_msg.ranges.push_back(range_msg);
    }
    ultrasonics_writer_->Write((ultrasonic_msg));

    //

    // 然后您可以将这个消息发布到ROS话题上
    // pc_writer_->Write(range_msg);
}
