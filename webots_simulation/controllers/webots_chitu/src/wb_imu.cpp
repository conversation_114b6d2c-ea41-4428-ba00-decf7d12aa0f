#include "wb_imu.hpp"

DDSWbImu::DDSWbImu(webots::Robot *robot, int step, const UParam &param) : DDSWbSensor("imu",robot, step, param) {}

bool DDSWbImu::onInit()
{
    param_.param("wb_acc_name", wb_acc_name_, std::string(""));
    param_.param("wb_gyro_name", wb_gyro_name_, std::string(""));
    param_.param("wb_iu_name", wb_iu_name_, std::string(""));
    param_.param("topic_name", topic_name_, std::string(""));
    param_.param("link_name", link_name_, std::string(""));

    accelerometer_ = (webots::Accelerometer *)robot_->getDevice(wb_acc_name_);
    if (accelerometer_ == nullptr) return false;
    accelerometer_->enable(sensor_step_);
    gyro_ = (webots::Gyro *)robot_->getDevice(wb_gyro_name_);
    if (gyro_ == nullptr) return false;
    gyro_->enable(sensor_step_);
    iu_ = (webots::InertialUnit *)robot_->getDevice(wb_iu_name_);
    if (iu_ == nullptr) return false;
    iu_->enable(sensor_step_);

    writer_ = std::make_shared<uslam::transport::Writer<sensor_msgs::msg::Imu>>(topic_name_);

    printf("DDSWbImu::init step:%d acc:%s gyro:%s iu:%s topic_name:%s link_name:%s\n", sensor_step_,
           wb_acc_name_.c_str(), wb_gyro_name_.c_str(), wb_iu_name_.c_str(), topic_name_.c_str(), link_name_.c_str());
    return true;
}

void DDSWbImu::onPublish()
{
    const double *acc_value_ptr = accelerometer_->getValues();
    const double *gyro_value_ptr = gyro_->getValues();
    const double *orientation_value_ptr = iu_->getQuaternion();
    sensor_msgs::msg::Imu msg;
    unav::Time timestamp;
    timestamp.fromSec(robot_->getTime());
    msg.header.stamp = timestamp;
    msg.header.frame_id = link_name_;
    msg.orientation.x = *orientation_value_ptr;
    msg.orientation.y = *(orientation_value_ptr + 1);
    msg.orientation.z = *(orientation_value_ptr + 2);
    msg.orientation.w = *(orientation_value_ptr + 3);
    msg.linear_acceleration.x = *acc_value_ptr;
    msg.linear_acceleration.y = *(acc_value_ptr + 1);
    msg.linear_acceleration.z = *(acc_value_ptr + 2);
    msg.angular_velocity.x = *gyro_value_ptr;
    msg.angular_velocity.y = *(gyro_value_ptr + 1);
    msg.angular_velocity.z = *(gyro_value_ptr + 2);

    // point_cloud->height = 1;
    // point_cloud->width = lidar_->getNumberOfPoints();
    // point_cloud->header.stamp = static_cast<uint64_t>(points_ptr->time * 1e6);
    writer_->Write(msg);
}