

#include <wb_sensor.hpp>

using namespace webots;

// DDSWbSensor::DDSWbSensor(webots::Robot *robot, std::string name, int step, std::string topic_name, std::string link_name) :
//     robot_(robot), wb_name_(name), sensor_step_(step), topic_name_(topic_name), link_name_(link_name)
// {
// }
DDSWbSensor::DDSWbSensor(std::string name, webots::Robot *robot, int step, const UParam &param) :
    name_(name), robot_(robot), sensor_step_(step), param_(param)
{
}

bool DDSWbSensor::init()
{
    if (onInit()) inited_ = true;
    return inited_;
}

void DDSWbSensor::publishValue()
{
    if (!isStepReady()) return;
    onPublish();
}

bool DDSWbSensor::isStepReady()
{
    if (!inited_) {
        printf("DDSWbSensor::isStepReady inited_:%d\n", inited_);
        return false;
    }

    if (last_time_ == 0) {
        last_time_ = robot_->getTime();
        return true;
    }
    if ((robot_->getTime() - last_time_) * 1000.0 > sensor_step_ * 1.0 - 1e-6) {
        last_time_ = robot_->getTime();
        return true;
    } else {
        // printf("delta:%f\n", (robot_->getTime() - last_time_));
        return false;
    }
}
