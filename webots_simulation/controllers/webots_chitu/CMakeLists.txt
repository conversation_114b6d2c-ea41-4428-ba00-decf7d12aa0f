cmake_minimum_required(VERSION 3.10.0)
project(webots_chitu)
find_package(uauto REQUIRED)
find_package(uautopilot_msgs REQUIRED)
find_package(PCL REQUIRED)
find_package(OpenCV REQUIRED)
find_package(jsoncpp REQUIRED)
find_package(webots_objects_msgs REQUIRED)

include_directories(${WEBOTS_INCLUDES} include ${PCL_INCLUDE_DIRS} ${OpenCV_INCLUDE_DIRS})
add_executable(
  webots_chitu
  my_controller.cpp
  src/wb_camera.cpp
  src/wb_car.cpp
  src/wb_imu.cpp
  src/wb_lidar.cpp
  src/wb_gt.cpp
  src/wb_traffic_light.cpp
  src/wb_ultrasonic.cpp
  src/wb_sensor.cpp
  src/webots_module.cpp
  src/object_process.cpp)

target_link_libraries(webots_chitu ${WEBOTS_LIBRARIES} ${PCL_LIBRARIES} ${OpenCV_LIBS} jsoncpp_lib /usr/lib/x86_64-linux-gnu/libtcmalloc.so.4)

ament_target_dependencies(webots_chitu uautopilot_msgs uauto webots_objects_msgs)

install(TARGETS webots_chitu
        RUNTIME DESTINATION projects/controllers/webots_chitu)
install(FILES webots_chitu.yaml DESTINATION projects/controllers/webots_chitu)

if(INSTALL_CONTROLLER_TO_SOURCE_DIR)
  add_custom_command(
    TARGET webots_chitu
    POST_BUILD
    COMMAND ${CMAKE_COMMAND} -E copy ${CMAKE_CURRENT_BINARY_DIR}/webots_chitu
            ${CMAKE_CURRENT_SOURCE_DIR})
endif()
