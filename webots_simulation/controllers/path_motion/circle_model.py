import math


class CircleModel:
    """圆周运动模型类"""

    def __init__(self, radius=1.0, velocity=1.0, init_pos=(0, 0, 1.0)):
        """
        初始化圆周运动模型

        Args:
            radius (float): 运动半径(米)
            velocity (float): 运动速度(米/秒)
            center (tuple): 圆心坐标(x, y, z)
        """
        self.radius = radius
        self.velocity = velocity
        self.init_pos = init_pos
        self.angular_speed = self.velocity / self.radius

    def calculate_position(self, elapsed_time):
        """
        计算给定时间的位置

        Args:
            elapsed_time (float): 运行时间(秒)

        Returns:
            tuple: (x, y, z) 位置坐标
        """
        x = self.init_pos[0] + self.radius * math.cos(self.angular_speed * elapsed_time)
        y = self.init_pos[1] + self.radius * math.sin(self.angular_speed * elapsed_time)
        z = self.init_pos[2]
        return [x, y, z]

    def calculate_angle(self, elapsed_time):
        """
        计算圆周运动的旋转角度

        Args:
            elapsed_time (float): 已经过的时间(秒)

        Returns:
            float: 旋转角度(弧度)
        """
        # 获取当前时刻和稍后时刻的位置
        angle = self.angular_speed * elapsed_time + math.pi / 2
        return angle

    def calculate_velocity(self, elapsed_time):
        """
        计算圆周运动的速度

        Args:
            elapsed_time (float): 已经过的时间(秒)

        Returns:
            float: 线速度xyz(米/秒) 角速度（弧度/秒）
        """
        vx = -math.sin(elapsed_time / self.radius)
        vy = math.cos(elapsed_time / self.radius)
        vz = 0
        return [vx, vy, vz, 0, 0, self.angular_speed]
