#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/traffic/protos/PedestrianCrossing.proto
# keywords: traffic/sign
# A pedestrian crossing 20 x 8 meters (0.1m thick).
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/geometries/protos/TexturedBox.proto"

PROTO PedestrianCrossing [
  field SFVec3f    translation          0 0 0
  field SFRotation rotation             0 0 1 0
  field SFString   name                 "pedestrian crossing"
  field SFVec3f    scale                1 1 1
  field SFVec2f    size                 20 8                   # Defines the size of the pedestrian crossing.
  field SFInt32    textureFiltering     4                      # Defines the filtering level for the texture used for the pedestrian crossing.
  field SFBool     enableBoundingObject TRUE                   # Defines whether the pedestrian crossing should have a bounding object.
]
{
  %<
    let size = fields.size.value;
    if (size.x <= 0 || size.y <= 0) {
      size = fields.size.defaultValue;
      console.error('Both components of \'size\' must be strictly positive. Value reset to (' + size.x + ', ' + size.y + ').');
    }
  >%
  Solid {
    translation IS translation
    rotation IS rotation
    recognitionColors [ 0.2 0.2 0.2 ]
    children [
      DEF ZEBRA_TRANS Transform {
        translation 0 -0.1 0
        rotation -0.577350 0.577352 -0.577350 -2.094399
        scale IS scale
        children [
          Shape {
            appearance PBRAppearance {
              baseColorMap ImageTexture {
                url [
                  "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/traffic/protos/textures/crossing.png"
                ]
                filtering IS textureFiltering
              }
              metalness 0
              roughness 0.9
            }
            geometry TexturedBox {
              size 0.4 %<= size.x >% %<= size.y >%
              mapping "default"
              left FALSE
              right FALSE
              top FALSE
              bottom FALSE
            }
            castShadows FALSE
          }
        ]
      }
    ]
    name IS name
    model "pedestrian crossing"
    %< if (fields.enableBoundingObject.value) { >%
    boundingObject Group {
      children [
        Pose {
          translation 0 0 -0.1
          children [
            Box {
              size %<= size.x >% %<= size.y >% 0.4
            }
          ]
        }
        DEF RAMP1_TRANS Pose {
          translation 0 %<= size.y * 0.5 >% -0.2535
          rotation 1 0 0 2.3562
          children [
            DEF RAMP_BOX Box {
              size %<= size.x >% 0.5 0.5
            }
          ]
        }
        DEF RAMP2_TRANS Pose {
          translation 0 %<= -size.y * 0.5 >% -0.2535
          rotation 1 0 0 2.3562
          children [
            USE RAMP_BOX
          ]
        }
      ]
    }
    %< } >%
  }
}
