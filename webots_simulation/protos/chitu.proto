#VRML_SIM R2023a utf8
# Describe the functionality of your PROTO here.

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/Car.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/VehicleLights.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/vehicles/protos/abstract/VehicleWheel.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Plastic.proto"
EXTERNPROTO "VelodyneVLP-16.proto"
EXTERNPROTO "UltrasonicSensor-12.proto"


PROTO chitu [
  field SFVec3f translation 0 0 0.4
  field SFRotation rotation 0 0 1 0
  field SFString name "chitu"
  field SFString controller "webots_chitu"
  field MFString controllerArgs ["webots_chitu.yaml"]
  field MFNode extensionSlot [
    Group {
      children [
        Accelerometer {
          translation 2.2444 0 1.5
          rotation 0 0 1 -1.57
        }
        Gyro {
          translation 2.2444 0 1.5
          rotation 0 0 1 -1.57
        }
        InertialUnit {
          translation 2.2444 0 1.5
          rotation 0 0 1 -1.57
        }
      ]
    }
    UltrasonicSensor-12{
      name "UltrasonicSensor-12"
    }

    VelodyneVLP-16 {
      translation 2.3 0 1.65
      rotation 0 0 1 -1.5707
      name "laser_top"
    }
    VelodyneVLP-16 {
      translation 2.391 0.781 0.545
      rotation 0 0.0582949 -0.9982994  0.2752091
      name "laser_left"
    }
    VelodyneVLP-16 {
      translation 2.473 -0.785  0.465
      rotation  0.0090808 0.0110988 0.9998972  3.4103689
      name "laser_right"
    }
    Lidar {
      numberOfLayers 16
      maxRange 100
      type "rotating"
    }
    Camera {
      translation 2.5 0.12671 1.49354
      rotation 0.701414 -0.701414 -0.049637 0.049637
      width 1280
      height 720
      name "camera_0"
      fieldOfView 1.74
      recognition Recognition{
        occlusion 0
        segmentation TRUE
      }
    }
    Transform {
      translation 1.05 0 -0.25
      children [
        Shape {
          appearance Plastic {
          }
          geometry Mesh {
            url [
              "../protos/chitu_coma_rotate.dae"
            ]
          }
          castShadows FALSE
        }
      ]
    }
  ]
]
{
  Car {
    translation IS translation
    rotation IS rotation
    name IS name
    controller IS controller
    controllerArgs IS controllerArgs
    extensionSlot IS extensionSlot

    supervisor TRUE
    trackFront 1.4
    trackRear 1.4
    wheelbase 2.2
    minSteeringAngle -0.75
    maxSteeringAngle 0.75
    maxVelocity 10
    suspensionFrontSpringConstant 100000
    suspensionFrontDampingConstant 4000
    suspensionRearSpringConstant 100000
    suspensionRearDampingConstant 4000
    physics Physics {
      density -1
      mass 1000
      centerOfMass [
        1.05 0 0
      ]
    }
    boundingObject Group {
      children [
        Transform {
          translation 2.45 0 1.0
          rotation 1 0 0 0
          children [
            Box {
              size 0.5 1.5 1.2
            }
          ]
        }
        Transform {
          translation 1.05 0 0.2
          rotation 1 0 0 0
          children [
            Box {
              size 3.3 1.4 0.5
            }
          ]
        }
      ]
    }

    wheelFrontRight VehicleWheel { name "front right wheel" thickness 0.2 tireRadius 0.2835 rimRadius 0.2}
    wheelFrontLeft VehicleWheel { name "front left wheel" wheelSide TRUE thickness 0.2 tireRadius 0.2835 rimRadius 0.2}
    wheelRearRight VehicleWheel { name "rear right wheel" thickness 0.2 tireRadius 0.2835 rimRadius 0.2}
    wheelRearLeft VehicleWheel { name "rear left wheel" wheelSide TRUE thickness 0.2 tireRadius 0.2835 rimRadius 0.2}

  }

}
