#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/road/protos/RoadIntersection.proto
# keywords: traffic/road
# A simple and customizable roads intersection, all the road are crossing at the same point and equally separated in orientation.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Asphalt.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Road.proto"

PROTO RoadIntersection [
  field        SFVec3f              translation                    0 0 0
  field        SFRotation           rotation                       0 0 1 0
  field        SFString             name                           "road intersection"                   # Defines the intersection name.
  field        SFString             id                             ""                                    # Optionally defines a unique ID. A unique ID is required to use the SUMO exporter.
  field        MFString             connectedRoadIDs               []                                    # Optionally defines a list of the identifiers of the connected Road. This is required to use the SUMO exporter.
  field        SFInt32              roadNumber                     4                                     # Defines the number of intersecting roads.
  field        SFFloat              roadsWidth                     7                                     # Defines the width of the intersecting roads.
  field        SFFloat              speedLimit                     -1.0                                  # Optionally defines the speed limit. The recommended unit is meter per seconds.
  field        SFBool               startRoads                     TRUE                                  # Defines if the beginning of each roads should be included or not.
  field        MFFloat              startRoadsLength               [5]                                   # Defines the length of the intersecting roads. The same length is used for all the roads if this field has only one item.
  field        SFInt32              startRoadsNumberOfLanes        2                                     # Defines the number of lanes of the roads(used for the texture mapping).
  field        MFString             startRoadsStartLine            [ "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_dashed.png", # Optionally defines the texture used for the road line at the first way-point for each lane. If the string is empty, no road line will be added for the corresponding lane. The two textures `textures/road_line_dashed.png` and `textures/road_line_triangle.png` may be used in this field.
                                                                     "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/textures/road_line_triangle.png" ]
  field        MFString             startRoadsEndLine              []                                    # Optionally defines the texture used for the road line at the last way-point for each lane. If the string is empty, no road line will be added for the corresponding lane.
  field        MFNode{RoadLine {}}  startRoadsLine                 [ RoadLine { } ]                      # Defines the property of each line separating two lanes.
  field        SFBool               startRoadBorder                TRUE                                  # Defines whether the intersecting road should have a sidewalks.
  field        SFFloat              startRoadBorderHeight          0.15                                  # Defines the width of the intersecting road sidewalks.
  field        SFFloat              startRoadBorderWidth           0.8                                   # Defines the height of the intersecting road sidewalks.
  field        SFBool               startRoadBorderboundingObject  FALSE                                 # Defines whether the sidewalks should have a bounding object.
  field        SFBool               boundingObject                 FALSE                                 # Defines whether the intersecting roads should have a bounding object.
  field        SFBool               castShadows                    FALSE                                 # Defines whether the intersection should cast shadows.
  field        SFString             contactMaterial                "default"                             # Is `Solid.contactMaterial`.
  field        SFBool               bottom                         FALSE                                 # Defines whether the intersection bottom should be displayed (useful in case of bridge).
  field        SFBool               locked                         TRUE                                  # Is `Solid.locked`.
  field        SFNode               appearance                     Asphalt { }                           # Defines the appearance of the road.
]
{
  %<
    // fields checks
    let roadNumber = fields.roadNumber.value;
    if (roadNumber < 3) {
      roadNumber = fields.roadNumber.defaultValue;
      console.error('\'roadNumber\' must be greater than 3. Value reset to ' + roadNumber + '.');
    }

    let roadsWidth = fields.roadsWidth.value;
    if (roadsWidth < 0) {
      roadsWidth = fields.roadsWidth.defaultValue;
      console.error('\'roadsWidth\' must be greater or equal to 0. Value reset to ' + roadsWidth + '.');
    }

    let borderHeight = fields.startRoadBorderHeight.value;
    if (borderHeight < 0) {
      borderHeight = fields.startRoadBorderHeight.defaultValue;
      console.error('\'startRoadBorderHeight\' must be greater or equal to 0. Value reset to ' + borderHeight + '.');
    }

    let borderWidth = fields.startRoadBorderWidth.value;
    if (borderWidth < 0) {
      borderWidth = fields.startRoadBorderWidth.defaultValue;
      console.error('\'startRoadBorderWidth\' must be greater or equal to 0 Value reset to ' + borderWidth + '.');
    }

    let startRoadsLength = fields.startRoadsLength.value;
    let useIndependentStartRoadLength = true;
    let startRoadsLengthNB = startRoadsLength.length;
    if (startRoadsLengthNB === 0) {
      useIndependentStartRoadLength = false;
      startRoadsLength.push(0);
    } else if (startRoadsLengthNB < roadNumber) {
      useIndependentStartRoadLength = false;
      if (startRoadsLengthNB !== 1)
        console.error('\'startRoadsLength\' should either have 1 item or \'roadNumber\' items.')
    }

    if (useIndependentStartRoadLength) {
      for (let i = 0; i < roadNumber; ++i) {
        if (startRoadsLength[i] < 0) {
          startRoadsLength[i] = 0;
          console.error('\'startRoadsLength\' values should be greater or equal to 0. Value set to 0.');
        }
      }
    } else if (startRoadsLength[0] < 0) {
      console.error('\'startRoadsLength\' values should be greater or equal to 0.');
      startRoadsLength[0] = 0;
    }

    const boundingObject  = fields.boundingObject.value;
    const startRoads      = fields.startRoads.value;
    const startRoadBorder = fields.startRoadBorder.value;

    const innerRadius = roadsWidth / (2 * Math.tan(Math.PI/roadNumber));  // internal radius (used to compute middle of each roads)
    const outerRadius = roadsWidth / (2 * Math.sin(Math.PI/roadNumber));  // external radius (=> side of the road, used to compute the central polygone)
    const borderRadius = (roadsWidth + 2 * borderWidth) / (2 * Math.tan(Math.PI/roadNumber)); // radius where the border stops to intersect
    const roadMinimalLength = borderRadius - innerRadius;
    const textureCoordinateRatio = 0.2;
  >%
  Solid {
    translation IS translation
    rotation IS rotation
    recognitionColors [ 0.2 0.2 0.2 ]
    name IS name
    model "road intersection"
    children [
      Shape {
        appearance IS appearance
        geometry DEF INTERSECTION_GEOM IndexedFaceSet {
          coord Coordinate {
            point [
              0 0 0
              %< for (let i = 0; i <= roadNumber - 1; ++i) { >%
                %<= outerRadius * Math.sin(2 * Math.PI * i / roadNumber) >% %<= outerRadius * Math.cos(2 * Math.PI * i / roadNumber) >% 0
              %< } >%
            ]
          }
          texCoord TextureCoordinate {
            point [
              0 0
              %< for (let i = 0; i <= roadNumber - 1; ++i) { >%
                %<= outerRadius * Math.cos(2 * Math.PI * i / roadNumber) * textureCoordinateRatio >% %<= outerRadius * Math.sin(2 * Math.PI * i / roadNumber) * textureCoordinateRatio >%
              %< } >%
            ]
          }
          coordIndex [
          %< for (let i = 0; i <= roadNumber - 2; ++i) { >%
            0 %<= i + 2 >% %<= i + 1 >% -1
          %< } >%
            0 %<= 1 >% %<= roadNumber >% -1
          %< if (fields.bottom.value) { >%
            %< for (let i = 0; i <= roadNumber - 2; ++i) { >%
              0 %<= i + 1 >% %<= i + 2 >% -1
            %< } >%
              0 %<= roadNumber >% %<= 1 >% -1
          %< } >%
          ]
          texCoordIndex [
          %< for (let i = 0; i <= roadNumber - 2; ++i) { >%
            0 %<= i + 2 >% %<= i + 1 >% -1
          %< } >%
            0 %<= 1 >% %<= roadNumber >% -1
          %< if (fields.bottom.value) { >%
            %< for (let i = 0; i <= roadNumber - 2; ++i) { >%
              0 %<= i + 1 >% %<= i + 2 >% -1
            %< } >%
              0 %<= roadNumber >% %<= 1 >% -1
          %< } >%
          ]
          creaseAngle 0.2
        }
      }
      %< if (startRoads) { >%
        %< for (let i = 0; i <= roadNumber - 1; ++i) { >%
          %<
            let roadLength = useIndependentStartRoadLength ? startRoadsLength[i] : startRoadsLength[0];
          >%
          %< if (roadLength > 0) { >%
            # only for borders
            Road {
              translation %<= innerRadius * Math.sin(2 * Math.PI * i / roadNumber + Math.PI / roadNumber) >% %<= innerRadius * Math.cos(2 * Math.PI * i / roadNumber + Math.PI / roadNumber) >% 0
              rotation 0 0 1 %<= -2 * Math.PI * (i + 0.5) / roadNumber + Math.PI / 2>%
              name "border %<=i>%"
              wayPoints [
                0 0 0
                %< if (roadMinimalLength < roadLength) { >%
                  %<= roadMinimalLength >% 0 0
                %< } >%
                %<= roadLength >% 0 0
              ]
              leftBorderBoundingObject IS startRoadBorderboundingObject
              rightBorderBoundingObject IS startRoadBorderboundingObject
              road FALSE
              width %<= roadsWidth >%
              bottom IS bottom
              splineSubdivision -1
              %< if (startRoadBorder) { >%
                rightBorder TRUE
                leftBorder TRUE
                roadBorderHeight %<= borderHeight >%
                %< if (roadMinimalLength < roadLength) { >%
                  roadBorderWidth [ %<= 0 >%, %<= borderWidth >% ]
                %< } else { >%
                  roadBorderWidth [ %<= 0 >%, %<= borderWidth * (roadLength / roadMinimalLength) >% ]
                %< } >%
              %< } else { >%
                rightBorder FALSE
                leftBorder FALSE
              %< } >%
              castShadows IS castShadows
            }
          %< } >%
          # only for the road
          %< if (roadLength > 0) { >%
            Road {
              translation %<= innerRadius * Math.sin(2 * Math.PI * i / roadNumber + Math.PI / roadNumber) >% %<= innerRadius * Math.cos(2 * Math.PI * i / roadNumber + Math.PI / roadNumber) >% 0
              rotation 0 0 1 %<= -2 * Math.PI * (i + 0.5) / roadNumber + Math.PI / 2>%
              name "road %<= i >%"
              wayPoints [
                0 0 0
                %<= roadLength >% 0 0
              ]
              width %<= roadsWidth >%
              roadBoundingObject IS boundingObject
              contactMaterial IS contactMaterial
              bottom IS bottom
              numberOfLanes IS startRoadsNumberOfLanes
              lines IS startRoadsLine
              startLine IS startRoadsStartLine
              endLine IS startRoadsEndLine
              splineSubdivision -1
              rightBorder FALSE
              leftBorder FALSE
              appearance IS appearance
              castShadows IS castShadows
            }
          %< } >%
        %< } >%
      %< } >%
    ]
    locked IS locked
    %< if (boundingObject) { >%
      contactMaterial IS contactMaterial
      boundingObject USE INTERSECTION_GEOM
    %< } >%
  }
}
