#VRML_SIM R2023a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://www.cyberbotics.com/doc/guide/lidar-sensors#velodyne-puck
# Velodyne VLP-16 sensor model based on the Lidar PROTO.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).


EXTERNPROTO "../protos/VelodynePuck.proto"
PROTO VelodyneVLP-16 [
  field   SFVec3f    translation    0 0 0
  field   SFRotation rotation       0 0 1 0
  field   SFString   name           "Velodyne VLP-16"
  field   SFBool     enablePhysics  TRUE
]
{
  VelodynePuck {
    translation IS translation
    rotation IS rotation
    name IS name
    version "Puck"
    enablePhysics IS enablePhysics
  }
}
