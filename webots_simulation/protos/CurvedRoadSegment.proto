#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/road/protos/CurvedRoadSegment.proto
# keywords: traffic/road
# A simple curved road, the number of lanes, the curvature and the dimensions are configurable, an optional border can be enable.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/RoadLine.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Asphalt.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/appearances/protos/Pavement.proto"
EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/road/protos/Road.proto"

PROTO CurvedRoadSegment [
  field        SFVec3f              translation               0 0 0
  field        SFRotation           rotation                  0 0 1 0
  field        SFString             name                      "road"                  # Defines the road name.
  field        SFString             id                        ""                      # Optionally defines a unique ID. A unique ID is required to use the SUMO exporter.
  field        SFString             startJunction             ""                      # Optionally defines a reference to the Crossroad connected at the first Road waypoint. Setting correctly this field is required to use the SUMO exporter.
  field        SFString             endJunction               ""                      # Optionally defines a reference to the Crossroad connected at the last Road waypoint. Setting correctly this field is required to use the SUMO exporter.
  field        SFFloat              width                     7                       # Defines the total width of the road (excluding sidewalk).
  field        SFInt32              numberOfLanes             2                       # Defines the number of lanes (used for the texture mapping).
  field        SFInt32              numberOfForwardLanes      1                       # Defines the number of forward lanes. (this is an information with no impact on the graphical shape).
  field        SFFloat              speedLimit                -1.0                    # Optionally defines the speed limit. The recommended unit is meter per seconds.
  field        MFNode{RoadLine {}}  lines                     [ RoadLine { } ]        # Defines the property of each line separating two lanes.
  field        SFFloat              roadBorderHeight          0.15                    # Defines the height of the sidewalk.
  field        MFFloat              roadBorderWidth           [ 0.8 ]                 # Defines the width of the sidewalk.
  field        SFBool               rightBorder               TRUE                    # Defines whether the road should have a right sidewalk.
  field        SFBool               leftBorder                TRUE                    # Defines whether the road should have a left sidewalk.
  field        SFBool               rightBarrier              FALSE                   # Defines whether the road should have a right barrier.
  field        SFBool               leftBarrier               FALSE                   # Defines whether the road should have a left barrier.
  field        SFBool               bottom                    FALSE                   # Defines whether the road bottom should be displayed (useful in case of bridge).
  field        SFFloat              curvatureRadius           10                      # Defines the radius of the curve.
  field        SFFloat              totalAngle                1.5708                  # Defines the angle covered by the road (length = totalAngle * curvatureRadius).
  field        MFString             startLine                 []                      # Optionally defines the texture used for the road line at the first way-point for each lane. If the string is empty, no road line will be added for the corresponding lane. The two textures `textures/road_line_dashed.png` and `textures/road_line_triangle.png` may be used in this field.
  field        MFString             endLine                   []                      # Optionally defines the texture used for the road line at the last way-point for each lane. If the string is empty, no road line will be added for the corresponding lane.
  field        SFInt32              subdivision               16                      # Defines the degree of interpolation using B-Splines (if the value is lower than 0, the interpolation is disabled).
  field        SFFloat              tilt                      0                       # Defines the maximum tilt of the road.
  field        SFNode               appearance                Asphalt { }             # Defines the appearance of the road.
  field        SFNode               pavementAppearance        Pavement { }            # Defines the appearance of the sidewalk.
  field        MFString             bottomTexture             []                      # Defines the texture to be used for the bottom of the road.
  field        SFBool               locked                    TRUE                    # Is `Solid.locked`.
  field        SFBool               roadBoundingObject        FALSE                   # Defines whether the road should have a bounding object.
  field        SFBool               rightBorderBoundingObject FALSE                   # Defines whether the right sidewalk should have a bounding object.
  field        SFBool               leftBorderBoundingObject  FALSE                   # Defines whether the left sidewalk should have a bounding object.
  field        SFBool               rightBarrierBoundingObject TRUE                   # Defines whether the right crash barrier (if any) should have a bounding object.
  field        SFBool               leftBarrierBoundingObject TRUE                    # Defines whether the left crash barrier (if any) should have a bounding object.
  field        SFBool               castShadows               FALSE                   # Defines whether the road should cast shadows.
  field        SFString             contactMaterial           "default"               # Is `Solid.contactMaterial`.
]
{
  %<
    let radius = fields.curvatureRadius.value;
    if (radius < 0) {
      radius = fields.curvatureRadius.defaultValue;
      console.error('\'curvatureRadius\' must be strictly positive. Value reset to ' + radius + '.');
    }
    if (radius < fields.width.value * 0.5) {
      radius = fields.width.value * 0.5;
      console.error('\'radius\' must be greater than \'width / 2\'. Value reset to ' + radius + '.');
    }

    let angle = fields.totalAngle.value;
    if (angle < 0) {
      angle = fields.totalAngle.defaultValue;
      console.error('\'totalAngle\' must be greater or equal to zero. Value reset to ' + angle + '.');
    }

    let subdivision = fields.subdivision.value;
    if (subdivision < 1) {
      subdivision = fields.subdivision.defaultValue;
      console.error('\'subdivision\' must be greater or equal to 1. Value reset to ' + subdivision + '.');
    }
    const tilt = fields.tilt.value;
  >%
  Road {
    translation IS translation
    rotation IS rotation
    name IS name
    width IS width
    numberOfLanes IS numberOfLanes
    lines IS lines
    roadBorderHeight IS roadBorderHeight
    roadBorderWidth IS roadBorderWidth
    rightBorder IS rightBorder
    leftBorder IS leftBorder
    rightBarrier IS rightBarrier
    leftBarrier IS leftBarrier
    bottom IS bottom
    startLine IS startLine
    endLine IS endLine
    startingAngle 0
    endingAngle %<= angle >%
    wayPoints [
      %< for (let i = 0; i <= subdivision; ++i) { >%
        %<= radius * Math.sin(i * angle / subdivision) >% %<= radius * Math.cos(i * angle / subdivision) >% 0
      %< } >%
    ]
    roadTilt [
      %< for (let i = 0; i <= subdivision; ++i) { >%
        %<= tilt * (0.5 - Math.abs((i - (subdivision * 0.5))/subdivision)) >%
      %< } >%
    ]
    splineSubdivision -1
    appearance IS appearance
    pavementAppearance IS pavementAppearance
    locked IS locked
    roadBoundingObject IS roadBoundingObject
    rightBorderBoundingObject IS rightBorderBoundingObject
    leftBorderBoundingObject IS leftBorderBoundingObject
    rightBarrierBoundingObject IS rightBarrierBoundingObject
    leftBarrierBoundingObject IS leftBarrierBoundingObject
    contactMaterial IS contactMaterial
    castShadows IS castShadows
  }
}
