#VRML_SIM R2023b utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://webots.cloud/run?url=https://github.com/cyberbotics/webots/blob/released/projects/objects/advertising_board/protos/AdvertisingBoard.proto
# keywords: exterior/street furniture
# Double sided and adjustable advertising board with the possibility of adding up to four poles.
# Those poles can either be a `cylinder`, `box`, or an `H-shape`.
# template language: javascript

EXTERNPROTO "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/objects/geometries/protos/Extrusion.proto"

PROTO AdvertisingBoard [
  field SFVec3f     translation       0 0 0
  field SFRotation  rotation          0 0 1 0
  field SFString    name              "advertising board"                   # Defines the name of the advertising board.
  field MFString    frontTexture      "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/worlds/textures/webots_billboard.jpg"       # Defines the front display texture.
  field MFString    backTexture       "https://raw.githubusercontent.com/cyberbotics/webots/R2023b/projects/default/worlds/textures/webots_billboard.jpg"       # Defines the back display texture.
  field MFColor     recognitionColors [ 1 1 1, 0.75 0 0 ]                   # Is `Solid.recognitionColors`.
  field SFBool      displayBackLight  FALSE                                 # Defines wether the displays are backlit or not.
  field SFFloat     displayWidth      4                                     # Defines the width of the displays.
  field SFFloat     displayHeight     3                                     # Defines the height of the displays.
  field SFFloat     frameThickness    0.6                                   # Defines the thickness of the frame.
  field SFColor     frameColor        0.886275 0.886275 0.886275            # Defines the color of the outer frame of the board.
  field SFColor     poleColor         0.886275 0.886275 0.886275            # Defines the color of the pole if present.
  field SFInt32     poleNumber        0                                     # Defines the number of poles. Their position is defined by the number of poles. There can be up to 4 equally spaced poles per board.
  field SFString{"cylinder", "box", "H-shape"}
                    poleType          "cylinder"                            # Defines the shape and behavious of the pole(s). `box` and `cylinder` pole types are placed at the center of the board and `H-shape` is placed behind. In the case of `H-shape`, backTexture is null and a base is not defined.
  field SFFloat     poleHeight        5                                     # Defines the height of the pole.
  field SFFloat     poleThickness     0.4                                   # Defines the thickness of the pole (radius of the cylinder for `cylinder`).
  field SFFloat     baseRatio         3.2                                   # Defines the size of the base for `box` and `cylinder` poles. This field is based on a ratio of `poleThickness`.
]
{
  %<
    let displayWidth = fields.displayWidth.value;
    if (displayWidth <= 0.0) {
      displayWidth = fields.displayWidth.defaultValue;
      console.error('\'displayWidth\' should be strictly positive. Value reset to ' + displayWidth + '.');
    }
    let displayHeight = fields.displayHeight.value;
    if (displayHeight <= 0.0) {
      displayHeight = fields.displayHeight.defaultValue;
      console.error('\'displayHeight\' should be strictly positive. Value reset to ' + displayHeight + '.');
    }
    let frameThickness = fields.frameThickness.value;
    if (frameThickness < 0.1) {
      frameThickness = fields.frameThickness.defaultValue;
      console.error('\'frameThickness\' should be >= 0.1. Value reset to ' + frameThickness + '.');
    }
    let poleNumber = fields.poleNumber.value;
    if (poleNumber < 0 || poleNumber >= 5) {
      poleNumber = fields.poleNumber.defaultValue;
      console.error('\'poleNumber\' should be strictly positive and less than 5. Value reset to ' + poleNumber + '.');
    }
    const poleType = fields.poleType.value;
    let poleHeight = fields.poleHeight.value;
    if (poleHeight <= 0.0) {
      poleHeight = fields.poleHeight.defaultValue;
      console.error('\'poleHeight\' should be strictly positive. Value reset to ' + poleHeight + '.');
    }

    let offsetFront = 0.06; // provides depth to the display plane with regards to the frame
    let offsetBack = 0.06;

    let poleThickness = fields.poleThickness.value;
    if (poleThickness < 0.1) {
      poleThickness = fields.poleThickness.defaultValue;
      console.error('\'poleThickness\' should be >= 0.1. Value reset to ' + poleThickness + '.');
    } else if (poleThickness > (frameThickness - offsetFront) && poleNumber !== 0) {
      // following test takes into account offset to prevent pole from being larger than the real display thickness
      poleThickness = fields.poleThickness.defaultValue;
      console.error('\'poleThickness\' should be smaller than the frame. Value reset to ' + poleThickness + '.');
    } else if (poleThickness > (frameThickness - offsetBack) && poleNumber !== 0) {
      poleThickness = fields.poleThickness.defaultValue;
      console.error('\'poleThickness\' should be smaller than the frame. Value reset to ' + poleThickness + '.');
    }
    const halfPoleThickness = poleThickness * 0.5;
    const quarterPoleThickness = poleThickness * 0.25;

    let baseRatio = fields.baseRatio.value;
    if (baseRatio < 0.1) {
      baseRatio = fields.baseRatio.defaultValue;
      console.error('\'baseRatio\' should be strictly positive. Value reset to ' + baseRatio + '.');
    }
    let yPosition = [[0], [-displayWidth / 4, displayWidth / 4], [-displayWidth / 3, 0, displayWidth / 3],
                    [-(displayWidth / 2) + poleThickness, -displayWidth / 7, displayWidth / 7, (displayWidth / 2) - poleThickness]];

    let displayElevation = 0;
    // add an elevation parameter when there is a pole to adjust coordinate system
    if (poleNumber !== 0)
      displayElevation = poleHeight - 0.6 * displayHeight - 0.2;
    else
      displayElevation = displayHeight / 2 + 0.1
  >%
  Solid {
    translation IS translation
    rotation IS rotation
    recognitionColors IS recognitionColors
    children [
      DEF BOARD Solid {
        translation 0 0 %<= displayElevation >%
        name "board"
        children [
          DEF LEFT_SIDE Pose {
            translation 0 %<= -0.05 - displayWidth / 2 >% 0
            children [
              DEF SHORT_SIDE Shape {
                appearance DEF COLOR_SIDE PBRAppearance {
                  baseColor IS frameColor
                  metalness 0
                  roughness 0.6
                }
                geometry Box {
                  size %<= frameThickness >% 0.1 %<= 0.2 + displayHeight >%
                }
              }
            ]
          }
          DEF RIGHT_SIDE Pose {
            translation 0 %<= 0.05 + displayWidth / 2 >% 0
            children [
              USE SHORT_SIDE
            ]
          }
          DEF TOP_SIDE Pose {
            translation 0 0 %<= 0.05 + displayHeight / 2 >%
            children [
              DEF LONG_SIDE Shape {
                appearance USE COLOR_SIDE
                geometry Box {
                  size %<= frameThickness >% %<= displayWidth >% 0.1
                }
              }
            ]
          }
          DEF BOTTOM_SIDE Pose {
            translation 0 0 %<= -0.05 - displayHeight / 2 >%
            children [
              USE LONG_SIDE
            ]
          }
          %<
            if (fields.frontTexture.value.length === 0) {
              // get length to decide whether an url is present or not.
              // if test is true, the frame indentation is deleted
              offsetFront = 0;
            }
          >%
          DEF FRONT_SIDE Pose {
            translation %<= frameThickness/2 - offsetFront >% 0 0
            rotation 0.577351 0.577351 0.577349 2.0944
            children [
              DEF FRONT_PLANE Shape {
                appearance PBRAppearance {
                  baseColor 1 1 1
                  %< if (fields.displayBackLight.value && offsetFront !== 0) { >%
                  emissiveColorMap ImageTexture {
                    url IS frontTexture
                  }
                  %< } >%
                  emissiveIntensity 5
                  metalness 0
                  roughness 0.6
                  baseColorMap ImageTexture {
                    url IS frontTexture
                  }
                }
                geometry DEF PLANE_GEOMETRY Plane {
                  size %<= displayWidth >% %<= displayHeight >%
                }
              }
            ]
          }
          %<
            if (fields.backTexture.value.length === 0 || poleType === 'H-shape') {
              // Manually setting the display offset for the H-shape because backTexture
              // is still present in the arguments, thus not allowing the loop to detect
              // the absence of texture.
              offsetBack = 0;
            }
          >%
          DEF BACK_SIDE Pose {
            translation %<= -frameThickness/2 + offsetBack >% 0 0
            rotation 0.577351 -0.577351 -0.577349 2.0944
            children [
              DEF BACK_PLANE Shape {
                  appearance PBRAppearance {
                  baseColor 1 1 1
                  %< if (fields.displayBackLight.value && offsetFront !== 0) { >%
                    emissiveColorMap ImageTexture {
                      url IS backTexture
                    }
                  %< } >%
                  emissiveIntensity 5
                  metalness 0
                  roughness 0.6
                  %< if (poleType === 'cylinder' || poleType === 'box') { >%
                    baseColorMap ImageTexture {
                      url IS backTexture
                    }
                  %< } >%
                }
                geometry USE PLANE_GEOMETRY
              }
            ]
          }
        ]
        boundingObject Box {
          size %<= frameThickness >% %<= displayWidth + 0.2 >% %<= displayHeight + 0.2 >%
        }
      }
      %< for (let i = 0; i < poleNumber; ++i) { >%
          %<
            let offsetPole = 0;
            // Add offset for the H-shaped pole for esthetic reasons
            if (poleType === 'H-shape')
              offsetPole = - 0.82 * frameThickness;
          >%
          DEF %<= 'POLE_' + i >% Solid {
            translation  %<= offsetPole >% %<= yPosition[poleNumber - 1][i] >% %<= poleHeight / 2 >%
            name "pole %<= i >%"
            children [
              Shape {
                appearance PBRAppearance {
                  baseColor IS poleColor
                  roughness 0.6
                  metalness 0
                }
                # Choose the type/geometry of pole(s)
                %< if (poleType === 'cylinder') { >%
                    geometry DEF CYLINDRICAL_POLE Cylinder {
                      height %<= poleHeight >%
                      radius %<= halfPoleThickness >%
                    }
                %< } else if (poleType === 'box') { >%
                    geometry DEF SQUARE_POLE Box {
                      size %<= poleThickness >% %<= poleThickness >% %<= poleHeight >%
                    }
                %< } else if (poleType === 'H-shape') { >%
                    geometry Extrusion {
                      crossSection [
                        %<= -halfPoleThickness >% %<= halfPoleThickness >%
                        %<= halfPoleThickness >% %<= halfPoleThickness >%
                        %<= halfPoleThickness >% %<= quarterPoleThickness >%
                        %<= quarterPoleThickness >% %<= quarterPoleThickness >%
                        %<= quarterPoleThickness >% %<= -quarterPoleThickness >%
                        %<= halfPoleThickness >% %<= -quarterPoleThickness >%
                        %<= halfPoleThickness >% %<= -halfPoleThickness >%
                        %<= -halfPoleThickness >% %<= -halfPoleThickness >%
                        %<= -halfPoleThickness >% %<= -quarterPoleThickness >%
                        %<= -quarterPoleThickness >% %<= -quarterPoleThickness >%
                        %<= -quarterPoleThickness >% %<= quarterPoleThickness >%
                        %<= -halfPoleThickness >% %<= quarterPoleThickness >%
                        %<= -halfPoleThickness >% %<= halfPoleThickness >%
                      ]
                      spine [
                        0 0 %<= (poleHeight / 2) >%
                        0 0 %<= -(poleHeight / 2) >%
                      ]
                    }
                %< } >%
              }
            ]
            # Choose the boundingObject geometry of pole(s)
            %< if (poleType === 'cylinder') { >%
                boundingObject USE CYLINDRICAL_POLE
            %< } else if (poleType === 'box') { >%
                boundingObject USE SQUARE_POLE
            %< } else if (poleType === 'H-shape') { >%
                # SQUARE_POLE not defined in case of H-shaped pole
                boundingObject DEF SQUARE_POLE Box {
                  size %<= poleThickness >% %<= poleThickness >% %<= poleHeight >%
                }
            %< } >%
          }
          # Choose the type/geometry of the poles' bases
          %< if (poleType === 'cylinder' && baseRatio > 1) { >%
            DEF %<= 'CYLINDER_BASE_' + i  >% Pose {
              translation 0 %<= yPosition[poleNumber - 1][i] >% 0.01
              children [
                Shape {
                  appearance PBRAppearance {
                    baseColor IS poleColor
                    metalness 0
                    roughness 0.6
                  }
                  geometry Cylinder {
                    height 0.02
                    radius %<= baseRatio * (poleThickness / 2) >%
                    subdivision 16
                  }
                }
              ]
            }
          %< } else if (poleType === 'box' && baseRatio > 1) { >%
            DEF %<= 'BOX_BASE' + i  >% Pose {
              translation 0 %<= yPosition[poleNumber - 1 ][i] >% 0.01
              children [
                Shape {
                  appearance PBRAppearance {
                    baseColor IS poleColor
                    metalness 0
                    roughness 0.6
                  }
                  geometry Box {
                    size %<= baseRatio * poleThickness >% %<= baseRatio * poleThickness >% 0.02
                  }
                }
              ]
            }
          %< } >%
      %< } >%
    ]
    name IS name
    model "advertising board"
  }
}
