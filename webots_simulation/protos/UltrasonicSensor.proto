#VRML_SIM R2023a utf8
# license: Copyright Cyberbotics Ltd. Licensed for use only with Webots.
# license url: https://cyberbotics.com/webots_assets_license
# documentation url: https://www.cyberbotics.com/doc/guide/lidar-sensors#velodyne-puck
# Velodyne VLP-16 sensor model based on the Lidar PROTO.
# This model was sponsored by the CTI project RO2IVSim (http://transport.epfl.ch/simulator-for-mobile-robots-and-intelligent-vehicles).


PROTO UltrasonicSensor [
  field SFVec3f translation 0 0 0
  field SFRotation rotation 0 0 0 0
  field SFString name "UltrasonicSensor"
]
{
  DistanceSensor {
    translation IS translation
    rotation IS rotation
    name IS name
    type   "generic"
    numberOfRays 1
    lookupTable [ 0     0  0,
                40    40 0.0  ]
    children [
      Shape {
        appearance Appearance {
          material Material {
            diffuseColor 1 0 0
          }
        }
        geometry Box {
            size 0.1 0.2 0.1
          }
    }
    ]
  }
}
