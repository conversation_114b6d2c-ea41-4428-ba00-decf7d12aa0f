# webots_simulation

## 仿真运行流程

### 运行环境

- 使用 udoke 构建镜像
- metafile.yml文件示例 [仿真的示例文件](http://gitlab.rd.ubtrobot.com/USLAM_GEN/uautopilot/udoke_template) ，其中包括webots、ros2_humble、core三个容器。
- 修改metafile.yml文件，可修改映射的文件夹 和 gpu 等配置

* 注：其中需要使用显卡的的机器，metafile 中 use_gpus 需要改为true，然后拉取镜像才能成功使用gpu，在进行联合仿真时可以以正常速度运行不卡顿。

### 仿真环境启动

#### webots

进入 webots 容器后拉取相关[仿真代码](http://gitlab.rd.ubtrobot.com/USLAM_GEN/uautopilot/uautopilot_simulaBashtion)，进行编译和运行

```bash
<NAME_EMAIL>:USLAM_GEN/uautopilot/uautopilot_simulation.git
cd ~/uautopilot_simulation
# 编译，该文件中需要拉取ubt_msgs文件，需要相关权限
./build_webots.sh
# 运行默认环境 该脚本运行 city_traffic.wbt 环境
./run_webots.sh
# 如果运行其他环境
source /opt/uauto/setup.bash
webots webots_simulation/worlds/xxx.wbt
```

**效果如下：**

<img src="docs/webots.png" width="550"/>

####  ros2_humble

进入 ros2_humble 容器后拉取相关[ros2 可视化面板代码](http://gitlab.rd.ubtrobot.com/USLAM_GEN/uautopilot/uautopilot_ros2_ws)，进行编译和运行

```bash
<NAME_EMAIL>:USLAM_GEN/uautopilot/uautopilot_ros2_ws.git
cd ~/uautopilot_ros2_ws
# 编译
colcon build
# 运行
source install/setup.bash
rviz2 -d rviz/navigation.rviz
# navigation.rviz 包含任务下发面板和监控面板
# navigation_debug.rviz 包含任务下发面板和导航调试面板
```

#### core 算法运行环境

* 将标定文件 **chitu_factory**  放入 /etc 目录下 ，(文件夹在 simulation_file 中）

```bash
#标定文件 chitu_factory  放入 /etc 目录下
sudo cp -r ~/uautopilot/chitu_factory /etc
```

- 地图文件 **wb2** 放入主目录下 workspace/maps 目录下，(地图文件在 simulation_file 中，需要解压）

```bash
#  wb2主目录下 workspace/maps 目录下，第一次创建没有workspace目录，先运行一遍run_navigation_webots.sh
cp -r ~/uautopilot/wb2 ~/workspace/maps
```

- 运行uautopilot 系统

```bash
cd /opt/uautopilot/uautopilot_docker/script
./run_navigation_webots.sh
```

- 登陆**Supervisor**查看运行情况 

ip： core容器ip:9001 例如：http://**********:9001

账号：chitu

密码：123456

仿真中关闭systemmonitor_node，重启datacenter_node

**效果如下：**

<img src="docs/supervisor.png" width="550"/>



### 重定位和导航功能测试

1. 在rviz界面点击Engage Velocity
2. 加载地图，Map&Modes点击set current maps
3. 重定位，先使用<img src="docs/2d_est.png" />在小车附近标记，后在Localization点击manual relocate

4. 规划，先使用<img src="docs/2d_goal.png" />在车道线标记，后在Planning点击Routing Planning

**RVIZ操作如图**

<img src="docs/img1.png" width="550"/>

<img src="docs/img2.png" width="550"/>

<img src="docs/img3.png" width="550"/>

<img src="docs/img4.png" width="550"/>

<img src="docs/img5.png" width="550"/>

## 建图功能测试

### 启动建图节点

- 在webots容器中启动webots软件

- 运行core容器中run_mapping_webots脚本文件

启动建图脚本

```
cd /opt/uautopilot/uautopilot_docker/script
./run_mapping_webots.sh
```

单启建图节点

```
./bin/task_mapping3d_lviorf_node -p config_mapping3d_task_chitu_ls
```

建图脚本对应的参数配置文件路径：

```
share/config_mapping3d_task_gazebo/config/mapping3d/lio.yaml
```

- 启动ros2_humble中的rviz2，打开mapping.rviz 文件。

  `source /install/setup.bash`

  `source /opt/ros/humble/setup.bash`

  `rviz2`
  
  注意这里如果需要配置mapping.rviz需要source两个setup文件。

### 键盘控制小车

- 需要控制chitu时，关闭systemmonitor_node，重启datacenter_node，mapping3d_node为建图节点，在该节点可查看建图log。

![mapping01](docs/mapping01.png)

- 在rviz界面mapping模块点击Engage Velocity  。
- 另启一个ros2_humble容器并运行：

```
ros2 run teleop_twist_keyboard teleop_twist_keyboard /cmd_vel:=/twist_cmd_vel
```

### 建图并保存

- rviz上自定义地图名字后，start mapping开始建图，save mapping保存地图。

![mapping02](docs/mapping02.png)

- 如果要保存地图请查看supervisor中是否打印了save map success，并且日志会打印地图保存路径。如果保存地图未成功可能是进程被中断或者内存不足，尝试等待log打印success或者更换电脑。
- 如果有重复保存或reset操作，地图可能会被保存为defaultMaps，请注意查看log中保存的文件名。
- 地图保存在udoke内，可以从容器中copy到挂载的本地路径下。

## 仿真配置

本仿真采用webots C++ API 进行数据读取，读取数据后使用 uauto 通信组件进行数据的发布

### simulation 文件结构

* **protos**： 存放 webots 模型文件，如 **chitu.proto** 为 chitu 的 webots 模型文件，需要修改传感器配置参数和传感器布局可修改此文件
* **worlds：** 存放 webots 仿真世界的场景文件
  * **city_small.wbt**为小型静态城市场景
  * **city_traffic.wbt** 为带动态车流和红绿灯的城市场景
  * **city_no_traffic.wbt** 为静态城市场景
  * **city_single_lane.wbt**为双向单车道场景。

* **controllers：** 存放 webots 控制器，负责读取传感器数据并发布，并进行 webots 世界的操控

### 控制器配置文件 webots_chitu.yaml

该配置文件决定控制器发布什么数据

```yaml
sensor_list: [Car, Lidar, IMU, Object] # 需要激活的传感器

# 底盘驱动 定义输入和输出的 topic 名称
# chitu底盘为Ackermann结构, ackermann_twist_in_topic 供算法使用，twist_in_topic 方便遥控控制
Car:
  type: Car
  odom_out_topic: "odom"
  ackermann_odom_out_topic: "ackermann/odom"
  twist_in_topic: "twist_cmd_vel"
  ackermann_twist_in_topic: "uslam/monitor/ackermann_cmd_vel"

# 3D 雷达
# wb_lidar_name 为 proto 内的传感器名称
# topic_name 为 uauto 发布的 topic 名称
# link_name 为 topic 的 tf frame_id
Lidar:
  type: Lidar
  wb_lidar_name: "laser_top"
  topic_name: "/sensor/lslidar_point_cloud/top/raw"
  link_name: "laser_link_top"

# IMU传感器，在 webots 中由加速度计、陀螺仪和惯性测量模块三个组成
IMU:
  type: IMU
  wb_acc_name: "accelerometer"
  wb_gyro_name: "gyro"
  wb_iu_name: "inertial unit"
  topic_name: "/sensor/imu"
  link_name: "imu_link"

# 相机，可开启图像分割功能
Camera:
  type: CAMERA
  wb_camera_name: "camera_0"
  topic_name: "/sensor/camera_0"
  link_name: "camera0_link"
  obtain_drivable_space_msg: true
  drivable_space_topic_name: "/multi_task_drivable_space_topic_"
  drivable_space_image_topic_name: "/webots_drivable_space_image_topic_"
  obtain_road_line_msg: false
  road_line_topic_name: "multi_task_lane_topic_"

# 真值发布
# base_robot_def 为真值发布的基准，webots 中的对象名称
# target_def_prefix_list 需要搜索的物体前缀列表，程序将搜索 prefix + [0-100]的物体
# x_min、x_max、y_min、y_max 只输出当前基准坐标系周围一定范围的物体
Object:
  type: GT
  base_robot_def: "WEBOTS_VEHICLE0"
  target_def_prefix_list: ["SUMO_VEHICLE", "BOX"]
  topic_name: "multi_task_objects_topic_"
  link_name: base_link
  x_min: -30.0
  x_max: 30.0
  y_min: -30.0
  y_max: 30.0
  skip_cnt: 10

TL:
  type: TrafficLight
  base_robot_def: "WEBOTS_VEHICLE0"
  target_def_prefix: "TRAFFIC_"
  topic_name: "multi_task_objects_topic_"

```

### 关于 GT 真值发布

**功能：**

* 搜索固定前缀的物体，发布其真实的位置，发布的基准坐标系为 **chitu 车体 base_link**。

  * 代码逻辑 为先搜索物体的 bounding_object ，再搜索其下的 Box ，将坐标系统一后合并为最大包围框发布。

* 接收话题`wb_simulated_obstacle`，支持外部通过 topic 向 webots 添加或修改障碍物。

  * `wb_simulated_obstacle`类型为`std_msgs::msg::String`, topic 内容为json格式。

  * ```json
    {"id":-1,"position":{"theta":0.0,"x":0.0,"y":0.0},"size":{"x":1.0,"y":1.0,"z":1.0},"velocity":1.0}
    ```

  * id = -1 时为添加障碍物模式，按照设定的位置、大小和速度新增障碍物，id 由 程序自动设定，可从rviz2中看见。**位置需要为相对于车体的相对位置。**

  * id >0 时为修改障碍物模式，请输入需要修改的障碍物id（从rviz2读），将重设障碍物位置、大小和速度

  * 可视化的配置在` uautopilot_ros2_ws `的 **Simulated Object Tool** 中

## webots 使用教程

### webots 官方资料

教程：https://cyberbotics.com/doc/guide/index

API文档：https://cyberbotics.com/doc/reference/index

车辆相关文档：https://cyberbotics.com/doc/automobile/index

### WorldInfo:

定义整个世界的物理配置，如坐标系、重力、摩擦力、仿真间隔等。

* FPS：显示帧率

* basicTimeStep：仿真步长，单位ms，设置为10则代表时间运行步长为10ms一个，如果仿真速率低，远低于1X，可适当增加步长减少计算量。

### 关于图像分割

Camera的图像分割来源于世界中物体的颜色设置，不是进行检测运算得出的。

如果需要分割产生效果，需要设置待分割物体的**recognitionColors**字段。

### Supervisor

设置了**supervisor TRUE**的物体，其控制器可以拥有整个webots的控制权限，可读取世界中各个物体的位置信息，往世界中添加或删除物体。

具体接口看文档：https://cyberbotics.com/doc/reference/supervisor

本仿真中，Chitu 车体和 SUMO 交通流控制器必须设为supervisor

### SUMO

sumo 为一个交通流软件，在webots中负责车流的生成和控制

使用文档：https://cyberbotics.com/doc/automobile/sumo-interface

#### sumo配置：

在webots 中 SumoInterface 节点进行配置：

**maxVehicles**：设置场景中最大出现的车辆数目

**GUI：** 开启可看sumo端的车辆管理gui界面

![image-20240718113009702](docs/image-20240718113009702.png)

![image-20240718113009702](docs/vokoscreen-2024-07-25_15-47-17.mp4)

如果需要修改车辆行驶速度，请修改`webots_simulation/worlds/city_traffic_net/sumo.net.xml`中各个车道的**speed**限速，单位为km/h

#### 车辆同步：

车位置和行为在sumo控制器和webots两边自动同步

sumo创建的车同步到webots中，DEF前缀为DEF为**SUMO_VEHICLEX**(X为0开始的数字)

webots中DEF为**WEBOTS_VIHICLEX**(X为0开始的数字)的车，也会自动同步进SUMO，参与绕障跟随模型（所以chitu车一定要设置为WEBOTS_VIHICLE）

#### 交通灯同步

sumo中可创建交通灯，使车辆按照一定交通规则行驶，避免路口产生碰撞。每个交通灯在sumo中有固定的ID和index序号，ID和Index序号从**Netedit**中点击交通灯获取。

如果要把交通灯颜色同步到webots中，红绿灯的对应 `LEDs` 节点名称应遵循以下语法： `trafficLightID_trafficLightIndex_r/y/g`

具体请看SumoInterface的children字段
