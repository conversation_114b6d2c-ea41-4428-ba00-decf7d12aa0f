cmake_minimum_required(VERSION 3.10 FATAL_ERROR)
project(gazebo_ackermann_controller)
find_package(ament_cmake REQUIRED)
find_package(uautopilot_msgs REQUIRED)
find_package(uauto REQUIRED)

include_directories(
    include
)

add_library(gazebo_ackermann_controller_module SHARED
    src/gazebo_ackermann_controller_module.cpp
)

target_link_libraries(gazebo_ackermann_controller_module
    uautopilot_msgs::uautopilot_msgs
    uauto::uauto
)

add_executable(gazebo_ackermann_controller_node
    app/gazebo_ackermann_controller_node.cpp
)

target_link_libraries(gazebo_ackermann_controller_node
    gazebo_ackermann_controller_module
)

#############
## Install ##
#############

install(TARGETS gazebo_ackermann_controller_node gazebo_ackermann_controller_module
   RUNTIME DESTINATION bin
   LIBRARY DESTINATION lib
   ARCHIVE DESTINATION lib
)

ament_package()















