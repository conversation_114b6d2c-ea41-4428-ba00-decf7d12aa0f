/**
 *
 *   @file gazebo_ackermann_controller_module.cpp
 *   <AUTHOR> <PERSON><PERSON> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-01-09
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *   LOSS OF USE, D<PERSON>A, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#include "gazebo_ackermann_controller_module.h"

#include <cmath>

#include "utf/transform_broadcaster.h"
#include "utf/utf.h"

namespace uslam {
namespace module {

GazeboAckermannControllerModule::GazeboAckermannControllerModule(const std::string &module_name,
                                                                 const std::filesystem::path &config_package) :
    ModuleBase(module_name), last_odom_time_(-1.0), odom_x_(0), odom_y_(0), odom_theta_(0)
{
}

GazeboAckermannControllerModule::~GazeboAckermannControllerModule() {}

bool GazeboAckermannControllerModule::onInit()
{
    // reader
    ackermann_drive_stamped_reader_ = createReader<ackermann_msgs::msg::AckermannDriveStamped>(
        "/uslam/monitor/ackermann_cmd_vel",
        std::bind(&GazeboAckermannControllerModule::ackermannCmdCallback, this, std::placeholders::_1));

    twist_cmd_vel_reader_ = createReader<geometry_msgs::msg::Twist>(
        "/twist_cmd_vel", std::bind(&GazeboAckermannControllerModule::twistCmdVelCallback, this, std::placeholders::_1));
    dynamic_joint_state_reader_ = createReader<control_msgs::msg::DynamicJointState>(
        "/dynamic_joint_states",
        std::bind(&GazeboAckermannControllerModule::dynamicJointStateCallback, this, std::placeholders::_1));

    // writer
    position_controller_writer_ = createWriter<std_msgs::msg::Float64MultiArray>("/position_cont/commands");
    velocity_controller_writer_ = createWriter<std_msgs::msg::Float64MultiArray>("/velocity_cont/commands");
    ackermann_odometry_writer_ = createWriter<ackermann_msgs::msg::AckermannDriveOdometry>("/ackermann/odom");
    odometry_writer_ = createWriter<nav_msgs::msg::Odometry>("/odom");
    return true;
}

void GazeboAckermannControllerModule::dynamicJointStateCallback(const std::shared_ptr<control_msgs::msg::DynamicJointState> &msg)
{
    msg->header.stamp = unav::Time::now();
    control_msgs::msg::DynamicJointState state_msg = *msg;
    // state_msg.header.stamp = unav::Time::now();
    for (int i = 0; i < state_msg.joint_names.size(); i++) {
        if (state_msg.joint_names[i] == "left_steering_joint") {
            left_steering_angle_ = state_msg.interface_values[i].values[0];
        }
        if (state_msg.joint_names[i] == "right_steering_joint") {
            right_steering_angle_ = state_msg.interface_values[i].values[0];
        }
        if (state_msg.joint_names[i] == "left_front_axle") {
            if (state_msg.interface_values[i].interface_names[0] == "velocity") {
                left_front_wheel_velocity_ = state_msg.interface_values[i].values[0];
            } else {
                left_front_wheel_velocity_ = state_msg.interface_values[i].values[1];
            }
        }
        if (state_msg.joint_names[i] == "right_front_axle") {
            if (state_msg.interface_values[i].interface_names[0] == "velocity") {
                right_front_wheel_velocity_ = state_msg.interface_values[i].values[0];
            } else {
                right_front_wheel_velocity_ = state_msg.interface_values[i].values[1];
            }
        }
        if (state_msg.joint_names[i] == "left_rear_axle") {
            if (state_msg.interface_values[i].interface_names[0] == "velocity") {
                left_rear_wheel_velocity_ = state_msg.interface_values[i].values[0];
            } else {
                left_rear_wheel_velocity_ = state_msg.interface_values[i].values[1];
            }
        }
        if (state_msg.joint_names[i] == "right_rear_axle") {
            if (state_msg.interface_values[i].interface_names[0] == "velocity") {
                right_rear_wheel_velocity_ = state_msg.interface_values[i].values[0];
            } else {
                right_rear_wheel_velocity_ = state_msg.interface_values[i].values[1];
            }
        }
    }

    double realtime_center_y = tan(M_PI_2 - left_steering_angle_) * wheel_base_ + joint_dist_div_2_;
    double realtime_steering_angle = atan(wheel_base_ / realtime_center_y);
    // realtime_center_y = wheel_base_ * tan(M_PI_2 - realtime_steering_angle);
    double left_dist = realtime_center_y - joint_dist_div_2_;
    double gain = left_rear_wheel_velocity_ * (2 * M_PI * wheel_radius_) / left_dist;
    double realtime_veh_speed = gain * realtime_center_y / (2 * M_PI);

    ackermann_msgs::msg::AckermannDriveOdometry ackermann_odometry_msg;
    ackermann_odometry_msg.header.stamp = state_msg.header.stamp;
    ackermann_odometry_msg.header.frame_id = "base_link";
    ackermann_odometry_msg.steering_angle = realtime_steering_angle;
    ackermann_odometry_msg.speed = realtime_veh_speed;
    ackermann_odometry_writer_->Write(ackermann_odometry_msg);
    // ULOGI("realtime_center_y: %f, realtime_steering_angle: %f, realtime_veh_speed: %f", realtime_center_y, realtime_steering_angle, realtime_veh_speed);

    //解算位姿里程计odom
    if (last_odom_time_ < 0) {
        last_odom_time_ = unav::Time(state_msg.header.stamp).toSec();
        return;
    }

    double delta_t = unav::Time(state_msg.header.stamp).toSec() - last_odom_time_;

    double radius = wheel_base_ * tan(M_PI_2 - realtime_steering_angle);
    double delta_odom = realtime_veh_speed * delta_t;
    double delta_alpha = delta_odom / radius;
    double delta_y = radius - cos(delta_alpha) * radius;
    double delta_x = sin(delta_alpha) * radius;

    // rospy.logwarn("wheelbase:%f, steer_ang:%f, cot(steer_ang):%f", self._wheelbase, steer_ang, math.tan((pi/2) - steer_ang))

    odom_x_ += cos(odom_theta_) * delta_x - sin(odom_theta_) * delta_y;
    odom_y_ += sin(odom_theta_) * delta_x + cos(odom_theta_) * delta_y;
    odom_theta_ += delta_alpha;
    odom_theta_ = atan2(sin(odom_theta_), cos(odom_theta_));

    // ULOGI("odom_x:%f, odom_y:%f, odom_theta:%f", odom_x_, odom_y_, odom_theta_);
    nav_msgs::msg::Odometry odom_msg;
    odom_msg.header.frame_id = "odom";
    odom_msg.header.stamp = state_msg.header.stamp;
    odom_msg.child_frame_id = "base_link";
    odom_msg.pose.pose.position.x = odom_x_;
    odom_msg.pose.pose.position.y = odom_y_;
    odom_msg.pose.pose.orientation = tf2::createQuaternionMsgFromYaw(odom_theta_);

    double angular_z = realtime_veh_speed / realtime_center_y;
    double linear_x = sin(angular_z) * realtime_center_y;
    double linear_y = (1 - cos(angular_z)) * realtime_center_y;
    odom_msg.twist.twist.linear.x = linear_x;
    odom_msg.twist.twist.linear.y = linear_y;
    odom_msg.twist.twist.angular.z = angular_z;
    odometry_writer_->Write(odom_msg);

    // tf sendTransform
    utf::TransformBroadcaster tfb("odom");
    geometry_msgs::msg::TransformStamped transform;
    transform.header.frame_id = "odom";
    transform.header.stamp = state_msg.header.stamp;
    transform.child_frame_id = "base_link";
    transform.transform.translation.x = odom_x_;
    transform.transform.translation.y = odom_y_;
    transform.transform.translation.z = 0;
    transform.transform.rotation = odom_msg.pose.pose.orientation;

    tfb.sendTransform(transform);

    last_odom_time_ = unav::Time(state_msg.header.stamp).toSec();
}

void GazeboAckermannControllerModule::ackermannCmdCallback(const std::shared_ptr<ackermann_msgs::msg::AckermannDriveStamped> &msg)
{
    // ULOGI("steering_angle:%f, speed: %f", msg->drive().steering_angle(), msg->drive().speed());
    if (fabs(msg->drive.steering_angle) > max_steering_angle_) {
        ULOGE("received steering_angle: %f exceed to max_steering_angle: %f", msg->drive.steering_angle,
              max_steering_angle_);
        msg->drive.steering_angle = msg->drive.steering_angle > 0 ? max_steering_angle_ : -max_steering_angle_;
        // return;
    }

    unav::Time start = unav::Time::now();
    ackermann_drive_stamped_ = *msg;

    if (unav::Time(last_ackermann_drive_stamped_.header.stamp).toSec() < 1) {
        last_ackermann_drive_stamped_ = ackermann_drive_stamped_;
        return;
    }

    double delta_t = unav::Time(ackermann_drive_stamped_.header.stamp).toSec() -
                     unav::Time(last_ackermann_drive_stamped_.header.stamp).toSec();
    ULOGI_EVERY_N(50, "stamp: %d.%d, steering_angle:%f, speed:%f", msg->header.stamp.sec, msg->header.stamp.nanosec,
                  msg->drive.steering_angle, msg->drive.speed);

    last_ackermann_drive_stamped_ = ackermann_drive_stamped_;

    double left_steering, right_steering, center_y;
    calculateFrontWheelSteering(left_steering, right_steering, center_y);

    // send position controller msg
    std_msgs::msg::Float64MultiArray position_msg;
    position_msg.data.push_back(left_steering);
    position_msg.data.push_back(right_steering);
    position_controller_writer_->Write(position_msg);

    double left_front_vel, right_front_vel, left_rear_vel, right_rear_vel;
    calculateFourWheelVelocity(center_y, left_front_vel, right_front_vel, left_rear_vel, right_rear_vel);
    // send velocity controller msg
    std_msgs::msg::Float64MultiArray velocity_msg;
    // velocity_msg.data().push_back(left_front_vel);
    // velocity_msg.data().push_back(right_front_vel);
    velocity_msg.data.push_back(left_rear_vel);
    velocity_msg.data.push_back(right_rear_vel);
    velocity_controller_writer_->Write(velocity_msg);

    // ULOGI("left_steering: %f, right_steering: %f, left_rear_vel:%f, right_rear_vel:%f", left_steering, right_steering, left_rear_vel, right_rear_vel);

    unav::Time end = unav::Time::now();
    double period = end.toSec() - start.toSec();
    // ULOGI("period: %f", period);
}

double GazeboAckermannControllerModule::getSteerAng(double phi)
{
    if (phi >= 0.0) {
        return (M_PI_2 - phi);
    }

    return (-M_PI_2 - phi);
}

void GazeboAckermannControllerModule::calculateFrontWheelSteering(double &left_steering, double &right_steering,
                                                                  double &center_y)
{
    double theta;
    double ang_vel;
    if (ackermann_drive_stamped_.drive.steering_angle_velocity > 0) {
        double delta_t = unav::Time(ackermann_drive_stamped_.header.stamp).toSec() -
                         unav::Time(last_ackermann_drive_stamped_.header.stamp).toSec();
        float ang_vel =
            (ackermann_drive_stamped_.drive.steering_angle - last_ackermann_drive_stamped_.drive.steering_angle) / delta_t;
        ang_vel = std::max(-ackermann_drive_stamped_.drive.steering_angle_velocity,
                           std::min(ang_vel, ackermann_drive_stamped_.drive.steering_angle_velocity));
        theta = last_ackermann_drive_stamped_.drive.steering_angle + ang_vel * delta_t;
    } else {
        theta = ackermann_drive_stamped_.drive.steering_angle;
    }

    if (theta > max_steering_angle_) {
        theta = max_steering_angle_;
    }

    if (theta < -max_steering_angle_) {
        theta = -max_steering_angle_;
    }

    center_y = wheel_base_ * tan(M_PI_2 - theta);
    // ULOGI("theta:%f, wheel_base:%f, center_y:%f, joint_dist_div_2:%f", theta, wheel_base_, center_y, joint_dist_div_2_);
    left_steering = getSteerAng(atan(1.0 / wheel_base_ * (center_y - joint_dist_div_2_)));
    right_steering = getSteerAng(atan(1.0 / wheel_base_ * (center_y + joint_dist_div_2_)));
}

void GazeboAckermannControllerModule::calculateFourWheelVelocity(double center_y, double &left_front_vel,
                                                                 double &right_front_vel, double &left_rear_vel,
                                                                 double &right_rear_vel)
{
    double veh_speed;
    if (ackermann_drive_stamped_.drive.acceleration > 0.0) {
        double delta_t = unav::Time(ackermann_drive_stamped_.header.stamp).toSec() -
                         unav::Time(last_ackermann_drive_stamped_.header.stamp).toSec();
        double speed = ackermann_drive_stamped_.drive.speed;
        double last_speed = last_ackermann_drive_stamped_.drive.speed;
        float accel = (speed - last_speed) / delta_t;
        accel = std::max(-ackermann_drive_stamped_.drive.acceleration,
                         std::min(accel, ackermann_drive_stamped_.drive.acceleration));
        veh_speed = last_ackermann_drive_stamped_.drive.speed + accel * delta_t;
    } else {
        veh_speed = ackermann_drive_stamped_.drive.speed;
    }

    double left_dist = center_y - joint_dist_div_2_;
    double right_dist = center_y + joint_dist_div_2_;

    // left front wheel velocity
    double gain = (2 * M_PI) * veh_speed / abs(center_y);
    double r = sqrt(left_dist * left_dist + wheel_base_ * wheel_base_);
    left_front_vel = gain * r / (2 * M_PI * wheel_radius_);

    // right front wheel velocity
    r = sqrt(right_dist * right_dist + wheel_base_ * wheel_base_);
    right_front_vel = gain * r / (2 * M_PI * wheel_radius_);

    // left rear wheel velocity  左后轮和右后轮以及虚拟轮是相同的绕轴运动的角速度
    gain = (2 * M_PI) * veh_speed / center_y;
    left_rear_vel = gain * left_dist / (2 * M_PI * wheel_radius_);

    // right rear wheel velocity
    right_rear_vel = gain * right_dist / (2 * M_PI * wheel_radius_);
}

bool GazeboAckermannControllerModule::convertTransRotVelToSteeringAngle(double linear_x, double angular_z,
                                                                        double wheelbase, double &steering_angle)
{
    if (fabs(linear_x) < 1e-6 || fabs(angular_z) < 1e-6) {
        steering_angle = 0;
        return true;
    }

    double radius = fabs(linear_x / angular_z);
    if (radius < joint_dist_div_2_) {
        ULOGE("radius: %f smaller than joint_dist_div_2: %f", radius, joint_dist_div_2_);
        return false;
    }

    steering_angle = atan2(wheelbase, radius);
    if (angular_z < 0) {
        steering_angle = -steering_angle;
    }
    if (fabs(steering_angle) > max_steering_angle_) {
        ULOGE("steering_angle: %f exceed to the max_steering_angle limit: %f, try lower angular_z or higher speed",
              steering_angle, max_steering_angle_);
        return false;
    }
    ULOGI("radius: %f, steering_angle: %f", radius, steering_angle);
    return true;
}

void GazeboAckermannControllerModule::twistCmdVelCallback(const std::shared_ptr<geometry_msgs::msg::Twist> &msg)
{
    // ULOGI("%s:%d", __FUNCTION__, __LINE__);

    // ULOGI("ackermann msg stamp:%d.%d", ackermann_msg.header.stamp.sec(), ackermann_msg.header.stamp.nanosec());
    // ULOGI("linear_x:%f, angular_z:%f", msg->linear().x(), msg->angular().z());
    // double steering_angle;
    // if (convertTransRotVelToSteeringAngle(msg->linear().x(), msg->angular().z(), wheel_base_, steering_angle)) {
    //     ackermann_msgs::msg::AckermannDriveStamped ackermann_msg;
    //     ackermann_msg.header.stamp = unav::Time::now();
    //     ackermann_msg.drive().speed() = msg->linear().x();
    //     ackermann_msg.drive().steering_angle() = steering_angle;
    //     ackermann_drive_stamped_writer_->Write(ackermann_msg);
    // }

    ackermann_msgs::msg::AckermannDriveStamped ackermann_msg;
    ackermann_msg.header.stamp = unav::Time::now();
    ackermann_msg.drive.speed = msg->linear.x;
    //使用Z轴角速度分量数值代表前轮虚拟轮的转向角
    ackermann_msg.drive.steering_angle = msg->angular.z;
    ackermannCmdCallback(std::make_shared<ackermann_msgs::msg::AckermannDriveStamped>(ackermann_msg));
}

}  // namespace module
}  // namespace uslam