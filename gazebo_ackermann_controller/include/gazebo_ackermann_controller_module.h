/**
 *
 *   @file gazebo_ackermann_controller_module.h
 *   <AUTHOR> <PERSON><PERSON> (<EMAIL>)
 *   @brief
 *   @version 0.1
 *   @date 2023-01-09
 *
 *   (C) 2023 UBTECH.Co.Ltd. All rights reserved
 *
 *   By downloading, copying, installing or using the software you agree to this license.
 *   If you do not agree to this license, do not download, install,
 *   copy or use the software.
 *
 *   THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS IS" AND
 *   ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO, THE IMPLIED
 *   WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR PURPOSE ARE DISCLAIMED.
 *   IN NO EVENT SHALL THE COPYRIGHT OWNER OR CONTRIBUTORS BE LIABLE FOR ANY DIRECT,
 *   INDIRECT, INCIDENTAL, SPECIAL, EXEMPLARY, OR CONSEQUENTIAL DAMAGES
 *   (INCLUDING, BUT NOT LIMITED TO, PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES;
 *   LOSS OF USE, DATA, OR PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED
 *   AND ON ANY THEORY OF LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY,
 *   OR TORT (INCLUDING NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF
 *   THE USE OF THIS SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
 *
 */

#ifndef U_AUTOPILOT_GAZEBO_ACKERMANN_CONTROLLER_MODULE_H_
#define U_AUTOPILOT_GAZEBO_ACKERMANN_CONTROLLER_MODULE_H_

#include <memory>

#include "ackermann_msgs/msg/AckermannDriveOdometry.h"
#include "ackermann_msgs/msg/AckermannDriveStamped.h"
#include "control_msgs/msg/DynamicJointState.h"
#include "geometry_msgs/msg/Twist.h"
#include "nav_msgs/msg/Odometry.h"
#include "std_msgs/msg/Float64MultiArray.h"
#include "umodule/module_base.h"
#include "utransport/reader.h"

namespace uslam {
namespace module {

class GazeboAckermannControllerModule : public ModuleBase
{
public:
    GazeboAckermannControllerModule(const std::string &module_name, const std::filesystem::path &config_file);
    ~GazeboAckermannControllerModule();

private:
    bool onInit() override;
    void ackermannCmdCallback(const std::shared_ptr<ackermann_msgs::msg::AckermannDriveStamped> &msg);
    bool convertTransRotVelToSteeringAngle(double linear_x, double angular_z, double wheelbase, double &steering_angle);
    void twistCmdVelCallback(const std::shared_ptr<geometry_msgs::msg::Twist> &msg);
    void dynamicJointStateCallback(const std::shared_ptr<control_msgs::msg::DynamicJointState> &msg);
    void calculateFrontWheelSteering(double &left_steering, double &right_steering, double &center_y);
    void calculateFourWheelVelocity(double center_y, double &left_front_vel, double &right_front_vel,
                                    double &left_rear_vel, double &right_rear_vel);
    double getSteerAng(double phi);

    // reader
    std::shared_ptr<uslam::transport::Reader<ackermann_msgs::msg::AckermannDriveStamped>> ackermann_drive_stamped_reader_;
    std::shared_ptr<uslam::transport::Reader<geometry_msgs::msg::Twist>> twist_cmd_vel_reader_;
    std::shared_ptr<uslam::transport::Reader<control_msgs::msg::DynamicJointState>> dynamic_joint_state_reader_;
    // writer
    std::shared_ptr<uslam::transport::Writer<std_msgs::msg::Float64MultiArray>> position_controller_writer_;
    std::shared_ptr<uslam::transport::Writer<std_msgs::msg::Float64MultiArray>> velocity_controller_writer_;
    std::shared_ptr<uslam::transport::Writer<ackermann_msgs::msg::AckermannDriveOdometry>> ackermann_odometry_writer_;
    std::shared_ptr<uslam::transport::Writer<nav_msgs::msg::Odometry>> odometry_writer_;
    ackermann_msgs::msg::AckermannDriveStamped ackermann_drive_stamped_, last_ackermann_drive_stamped_;

    // state interface data
    double left_steering_angle_;
    double right_steering_angle_;
    double left_front_wheel_velocity_;
    double right_front_wheel_velocity_;
    double left_rear_wheel_velocity_;
    double right_rear_wheel_velocity_;

    double last_odom_time_;
    double odom_x_, odom_y_, odom_theta_;

    // params used, set it in config file
    const double wheel_base_ = 2.2;
    const double joint_dist_div_2_ = 0.67;       // 0.75+0.2
    const double max_steering_angle_ = 0.52359;  // 虚拟轮最大转向角30度
    const double wheel_radius_ = 0.2835;
};

}  // namespace module
}  // namespace uslam

#endif  // U_AUTOPILOT_GAZEBO_ACKERMANN_CONTROLLER_MODULE_H_